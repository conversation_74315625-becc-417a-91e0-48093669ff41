package ethsecp256k1

import (
	"fmt"
	"testing"
)

func BenchmarkGenerateKey(b *testing.B) {
	b.<PERSON>()
	for i := 0; i < b.N; i++ {
		if _, err := GenerateKey(); err != nil {
			b.<PERSON><PERSON>(err)
		}
	}
}

func BenchmarkPubKey_VerifySignature(b *testing.B) {
	privKey, err := GenerateKey()
	if err != nil {
		b.<PERSON><PERSON>(err)
	}
	pubKey := privKey.PubKey()

	b.<PERSON>set<PERSON>r()
	b.Report<PERSON>llo<PERSON>()
	for i := 0; i < b.N; i++ {
		msg := []byte(fmt.Sprintf("%10d", i))
		sig, err := privKey.Sign(msg)
		if err != nil {
			b.<PERSON>al(err)
		}
		pubKey.VerifySignature(msg, sig)
	}
}
