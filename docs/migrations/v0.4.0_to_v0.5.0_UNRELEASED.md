# Cosmos EVM v0.4.0 → v0.5.0 Migration (UNRELEASED)

## 0) Prep

- Create a branch: `git switch -c upgrade/evm-v0.5`.
- Ensure a clean build + tests green pre-upgrade.
- Snapshot your current params/genesis for comparison later.

---

## 1) Dependency bumps (go.mod)

- Bump `github.com/cosmos/evm` to v0.5.0 and run:

```bash
go mod tidy
```

---

## 2) App wiring in `app.go`

### Mempool

#### Minimal setups: nothing to change

If you use the default mempool wiring (no custom pools), your existing code continues to work. If `BlockGasLimit` is 0, it defaults to `100_000_000`. If `BroadCastTxFn` is not set, it's also set to a default value.

```go
mempoolConfig := &evmmempool.EVMMempoolConfig{
    AnteHandler:   app.GetAnteHandler(),
    BlockGasLimit: 100_000_000, // or 0 to use default
}
evmMempool := evmmempool.NewExperimentalEVMMempool(
    app.CreateQueryContext, logger, app.EVMKeeper, app.FeeMarketKeeper, app.txConfig, app.clientCtx, mempoolConfig
)
```

#### Advanced setups: migrate your customizations

PR [#496](https://github.com/cosmos/evm/pull/496) replaced pre-built pools with configs in `EVMMempoolConfig`:

- Replace pools with configs
  - Removed: `TxPool *txpool.TxPool`, `CosmosPool sdkmempool.ExtMempool`
  - Added: `LegacyPoolConfig *legacypool.Config`, `CosmosPoolConfig *sdkmempool.PriorityNonceMempoolConfig[math.Int]`

If you built custom pools yourself:

```diff
 mempoolConfig := &evmmempool.EVMMempoolConfig{
-   TxPool:     customTxPool,
-   CosmosPool: customCosmosPool,
+   LegacyPoolConfig: &legacyCfg,  // or nil for defaults
+   CosmosPoolConfig: &cosmosCfg,  // or nil for defaults
   AnteHandler:      app.GetAnteHandler(),
   BroadCastTxFn:    myBroadcast, // optional
 }
```

Example custom configs:

```go
// EVM legacy txpool tuning
legacyCfg := legacypool.DefaultConfig
legacyCfg.PriceLimit = 2
mempoolConfig.LegacyPoolConfig = &legacyCfg

// Cosmos priority mempool tuning
cosmosCfg := sdkmempool.PriorityNonceMempoolConfig[math.Int]{}
cosmosCfg.TxPriority = sdkmempool.TxPriority[math.Int]{
    GetTxPriority: func(goCtx context.Context, tx sdk.Tx) math.Int {
        // Custom priority function
    },
    Compare:  func(a, b math.Int) int { return a.BigInt().Cmp(b.BigInt()) },
    MinValue: math.ZeroInt(),
}
mempoolConfig.CosmosPoolConfig = &cosmosCfg

// Custom EVM broadcast (optional)
mempoolConfig.BroadCastTxFn = func(txs []*ethtypes.Transaction) error { return nil }
```

### Default Precompiles

Default precompiles have been moved to `/evm/precompiles/types/defaults.go` and the function name was
changed to `DefaultStaticPrecompiles`. The function signature has also changed, and now takes pointers
as inputs for the `Erc20Keeper` and `TransferKeeper`. Finally, the `WithStaticPrecompiles` builder
function can now happen *alongside the keeper instantiation*, and not after. The new wiring is shown below:

```go
	app.EVMKeeper = evmkeeper.NewKeeper(
		appCodec, keys[evmtypes.StoreKey], tkeys[evmtypes.TransientKey], keys,
		authtypes.NewModuleAddress(govtypes.ModuleName),
		app.AccountKeeper,
		app.PreciseBankKeeper,
		app.StakingKeeper,
		app.FeeMarketKeeper,
		&app.ConsensusParamsKeeper,
		&app.Erc20Keeper,
		tracer,
	).WithStaticPrecompiles(
		precompiletypes.DefaultStaticPrecompiles(
			*app.StakingKeeper,
			app.DistrKeeper,
			app.PreciseBankKeeper,
			&app.Erc20Keeper, // UPDATED
			&app.TransferKeeper, // UPDATED
			app.IBCKeeper.ChannelKeeper,
			app.GovKeeper,
			app.SlashingKeeper,
			appCodec,
		),
	)
```

---

## 3) Build & quick tests

```bash
go build ./...
```

Smoke test on a single node:
- Send a few EVM txs; confirm promotion/broadcast (or your `BroadCastTxFn`).
- Send Cosmos txs; confirm ordering reflects your `CosmosPoolConfig` (if customized).

