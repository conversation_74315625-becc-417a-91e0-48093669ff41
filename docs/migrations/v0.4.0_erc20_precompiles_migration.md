# ERC20 Precompiles Migration

## Breaking Change in v0.4.0

The storage mechanism for ERC20 dynamic and native precompiles has fundamentally changed in Cosmos EVM v0.4.0. This migration is mandatory for chains with existing ERC20 token pairs.

### Impact Assessment

**Affected Chains:**

- Chains with IBC tokens converted to ERC20
- Chains using token factory with ERC20 representations
- Any chain with existing `DynamicPrecompiles` or `NativePrecompiles` in parameter storage

**Known Issues if Not Migrated:**

- ERC20 balances will show as 0 when queried via EVM
- `totalSupply()` calls return 0
- Token transfers via ERC20 interface fail
- Native Cosmos balances remain intact but inaccessible via EVM

## Migration Overview

### Storage Changes

**Before (v0.3.x):**

- Precompiles stored as concatenated hex strings in parameter storage
- Keys: `"DynamicPrecompiles"` and `"NativePrecompiles"`
- Format: Multiple addresses concatenated as 42-character hex strings

**After (v0.4.0):**

- Dedicated prefix stores for each precompile type
- Keys: `types.KeyPrefixDynamicPrecompiles` and `types.KeyPrefixNativePrecompiles`
- Individual storage entries per address

## Implementation Guide

### Quick Start

The migration can be added to your existing upgrade handler:

```go
// In your upgrade handler
store := ctx.KVStore(storeKeys[erc20types.StoreKey])
const addressLength = 42 // "0x" + 40 hex characters

// Migrate dynamic precompiles (IBC tokens, token factory)
if oldData := store.Get([]byte("DynamicPrecompiles")); len(oldData) > 0 {
    for i := 0; i < len(oldData); i += addressLength {
        address := common.HexToAddress(string(oldData[i : i+addressLength]))
        erc20Keeper.SetDynamicPrecompile(ctx, address)
    }
    store.Delete([]byte("DynamicPrecompiles"))
}

// Migrate native precompiles
if oldData := store.Get([]byte("NativePrecompiles")); len(oldData) > 0 {
    for i := 0; i < len(oldData); i += addressLength {
        address := common.HexToAddress(string(oldData[i : i+addressLength]))
        erc20Keeper.SetNativePrecompile(ctx, address)
    }
    store.Delete([]byte("NativePrecompiles"))
}
```

<details>
<summary>📄 Complete Example Implementation (click to expand)</summary>

### Create Upgrade Handler

Create a new upgrade handler:

```go
// app/upgrades/v040/handler.go
package v040

import (
    "context"

    storetypes "cosmossdk.io/store/types"
    upgradetypes "cosmossdk.io/x/upgrade/types"
    sdk "github.com/cosmos/cosmos-sdk/types"
    "github.com/cosmos/cosmos-sdk/types/module"
    erc20keeper "github.com/cosmos/evm/x/erc20/keeper"
    erc20types "github.com/cosmos/evm/x/erc20/types"
    "github.com/ethereum/go-ethereum/common"
)

const UpgradeName = "v0.4.0"

func CreateUpgradeHandler(
    mm *module.Manager,
    configurator module.Configurator,
    keepers *UpgradeKeepers,
    storeKeys map[string]*storetypes.KVStoreKey,
) upgradetypes.UpgradeHandler {
    return func(c context.Context, plan upgradetypes.Plan, vm module.VersionMap) (module.VersionMap, error) {
        ctx := sdk.UnwrapSDKContext(c)
        ctx.Logger().Info("Starting v0.4.0 upgrade...")

        // Run standard module migrations
        vm, err := mm.RunMigrations(ctx, configurator, vm)
        if err != nil {
            return vm, err
        }

        // Migrate ERC20 precompiles
        if err := migrateERC20Precompiles(ctx, storeKeys[erc20types.StoreKey], keepers.Erc20Keeper); err != nil {
            return vm, err
        }

        ctx.Logger().Info("v0.4.0 upgrade complete")
        return vm, nil
    }
}
```

### Implement Migration

```go
// app/upgrades/v040/erc20_migration.go
package v040

import (
    sdk "github.com/cosmos/cosmos-sdk/types"
    storetypes "cosmossdk.io/store/types"
    erc20keeper "github.com/cosmos/evm/x/erc20/keeper"
    "github.com/ethereum/go-ethereum/common"
)

func migrateERC20Precompiles(
    ctx sdk.Context,
    storeKey *storetypes.KVStoreKey,
    erc20Keeper erc20keeper.Keeper,
) error {
    store := ctx.KVStore(storeKey)
    const addressLength = 42 // "0x" + 40 hex characters

    migrations := []struct {
        oldKey string
        setter func(sdk.Context, common.Address)
        description string
    }{
        {
            oldKey:      "DynamicPrecompiles",
            setter:      erc20Keeper.SetDynamicPrecompile,
            description: "dynamic precompiles (token factory, IBC tokens)",
        },
        {
            oldKey:      "NativePrecompiles",
            setter:      erc20Keeper.SetNativePrecompile,
            description: "native precompiles",
        },
    }

    for _, migration := range migrations {
        oldData := store.Get([]byte(migration.oldKey))
        if len(oldData) == 0 {
            ctx.Logger().Info("No legacy data found", "type", migration.description)
            continue
        }

        addressCount := len(oldData) / addressLength
        ctx.Logger().Info("Migrating precompiles",
            "type", migration.description,
            "count", addressCount,
            "data_length", len(oldData),
        )

        migratedCount := 0
        for i := 0; i < len(oldData); i += addressLength {
            if i+addressLength > len(oldData) {
                ctx.Logger().Error("Invalid data length",
                    "type", migration.description,
                    "position", i,
                    "remaining", len(oldData)-i,
                )
                break
            }

            addressStr := string(oldData[i : i+addressLength])
            address := common.HexToAddress(addressStr)

            // Validate address
            if address == (common.Address{}) {
                ctx.Logger().Warn("Skipping zero address",
                    "type", migration.description,
                    "raw", addressStr,
                )
                continue
            }

            // Migrate to new storage
            migration.setter(ctx, address)
            migratedCount++

            ctx.Logger().Debug("Migrated precompile",
                "type", migration.description,
                "address", address.String(),
                "index", migratedCount,
            )
        }

        // Clean up old storage
        store.Delete([]byte(migration.oldKey))
        ctx.Logger().Info("Migration complete",
            "type", migration.description,
            "migrated", migratedCount,
            "expected", addressCount,
        )
    }

    return nil
}
```

</details>

### Define Upgrade Keepers

```go
// app/upgrades/v040/types.go
package v040

import (
    erc20keeper "github.com/cosmos/evm/x/erc20/keeper"
    // ... other keeper imports
)

type UpgradeKeepers struct {
    Erc20Keeper erc20keeper.Keeper
    // ... other keepers needed for upgrade
}
```

### Register Upgrade Handler

```go
// app/app.go
import (
    v040 "github.com/yourchain/app/upgrades/v040"
)

func (app *App) RegisterUpgradeHandlers() {
    app.UpgradeKeeper.SetUpgradeHandler(
        v040.UpgradeName,
        v040.CreateUpgradeHandler(
            app.ModuleManager,
            app.configurator,
            &v040.UpgradeKeepers{
                Erc20Keeper: app.Erc20Keeper,
            },
            app.keys,
        ),
    )
}
```

## Testing

### Pre-Upgrade

```bash
# 1. Query existing token pairs
mantrachaind query erc20 token-pairs --output json | jq

# 2. Check ERC20 balances for a known address
cast call $TOKEN_ADDRESS "balanceOf(address)" $USER_ADDRESS --rpc-url http://localhost:8545

# 3. Export state for backup
mantrachaind export > pre-upgrade-state.json
```

### Post-Upgrade

```bash
# 1. Verify precompiles are accessible
cast call $TOKEN_ADDRESS "totalSupply()" --rpc-url http://localhost:8545

# 2. Check balance restoration
cast call $TOKEN_ADDRESS "balanceOf(address)" $USER_ADDRESS --rpc-url http://localhost:8545

# 3. Test token transfer
cast send $TOKEN_ADDRESS "transfer(address,uint256)" $RECIPIENT 1000 \
  --private-key $PRIVATE_KEY --rpc-url http://localhost:8545

# 4. Verify in exported state
mantrachaind export | jq '.app_state.erc20.dynamic_precompiles'
```

## Integration Test Example

```go
// tests/upgrade_test.go
package tests

import (
    "testing"
    "github.com/stretchr/testify/require"
    "github.com/ethereum/go-ethereum/common"
)

func TestERC20PrecompileMigration(t *testing.T) {
    // Setup test environment
    app, ctx := setupTestApp(t)

    // Create legacy storage entries
    store := ctx.KVStore(app.keys[erc20types.StoreKey])

    // Add test addresses in old format
    dynamicAddresses := []string{
        "******************************************",
        "******************************************",
    }
    dynamicData := ""
    for _, addr := range dynamicAddresses {
        dynamicData += addr
    }
    store.Set([]byte("DynamicPrecompiles"), []byte(dynamicData))

    // Run migration
    err := migrateERC20Precompiles(ctx, app.keys[erc20types.StoreKey], app.Erc20Keeper)
    require.NoError(t, err)

    // Verify migration
    migratedAddresses := app.Erc20Keeper.GetDynamicPrecompiles(ctx)
    require.Len(t, migratedAddresses, len(dynamicAddresses))

    for i, addr := range dynamicAddresses {
        require.Equal(t, addr, migratedAddresses[i])
    }

    // Verify old storage is cleaned
    oldData := store.Get([]byte("DynamicPrecompiles"))
    require.Nil(t, oldData)
}
```

## References

- **GitHub Issue:** [#424](https://github.com/cosmos/evm/issues/424)
- **Reference Implementation:** [MANTRA-Chain PR #409](https://github.com/MANTRA-Chain/mantrachain/pull/409)
- **Test Suite:** [MANTRA-Chain E2E Tests](https://github.com/MANTRA-Chain/mantrachain-e2e/pull/41)

## Verification Checklist

- [ ] Test migration on testnet first
- [ ] Document all existing token pairs
- [ ] Verify ERC20 balances post-upgrade
- [ ] Test token transfers work
- [ ] Confirm IBC token conversions function
