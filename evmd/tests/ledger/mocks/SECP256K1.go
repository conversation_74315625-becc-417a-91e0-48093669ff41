// Code generated by mockery v2.15.0. DO NOT EDIT.

package mocks

import (
	"errors"

	mock "github.com/stretchr/testify/mock"
)

var ErrMockedSigning = errors.New("mocked signing error")

// SECP256K1 is an autogenerated mock type for the SECP256K1 type
type SECP256K1 struct {
	mock.Mock
}

// Close provides a mock function with given fields:
func (_m *SECP256K1) Close() error {
	ret := _m.Called()

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetAddressPubKeySECP256K1 provides a mock function with given fields: _a0, _a1
func (_m *SECP256K1) GetAddressPubKeySECP256K1(_a0 []uint32, _a1 string) ([]byte, string, error) {
	ret := _m.Called(_a0, _a1)

	var r0 []byte
	if rf, ok := ret.Get(0).(func([]uint32, string) []byte); ok {
		r0 = rf(_a0, _a1)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	var r1 string
	if rf, ok := ret.Get(1).(func([]uint32, string) string); ok {
		r1 = rf(_a0, _a1)
	} else {
		r1 = ret.Get(1).(string)
	}

	var r2 error
	if rf, ok := ret.Get(2).(func([]uint32, string) error); ok {
		r2 = rf(_a0, _a1)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// GetPublicKeySECP256K1 provides a mock function with given fields: _a0
func (_m *SECP256K1) GetPublicKeySECP256K1(_a0 []uint32) ([]byte, error) {
	ret := _m.Called(_a0)

	var r0 []byte
	if rf, ok := ret.Get(0).(func([]uint32) []byte); ok {
		r0 = rf(_a0)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func([]uint32) error); ok {
		r1 = rf(_a0)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

func (_m *SECP256K1) SignSECP256K1(_a0 []uint32, _a1 []byte, _a2 byte) ([]byte, error) {
	args := _m.Called(_a0, _a1)
	e := args.Get(1)
	err, _ := e.(error)
	if errors.Is(err, ErrMockedSigning) {
		return nil, err
	}
	f, _ := args.Get(0).(func([]uint32, []byte) ([]byte, error))
	return f(_a0, _a1)
}

type mockConstructorTestingTNewSECP256K1 interface {
	mock.TestingT
	Cleanup(func())
}

// NewSECP256K1 creates a new instance of SECP256K1. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewSECP256K1(t mockConstructorTestingTNewSECP256K1) *SECP256K1 {
	mock := &SECP256K1{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
