---
name: Issue
about: Report a bug or request a feature
title: "[Bug] <summary>" or "[Feature] <summary>"
labels: needs-triage
assignees: ''
---

## Type

<!-- Please check one -->
- [ ] Bug
- [ ] Feature
- [ ] Proposal / Discussion

## Summary

<!--
Describe the issue or request in 1–2 sentences.
Include relevant context or background.
-->

## Reproduction (for bugs)

<!--
Include minimal steps to reproduce the bug. Example:

1. Run `make start`
2. Submit a tx via `evm.send()`
3. Observe panic in logs
-->

## Impact

<!--
Explain the potential severity and who is affected.
E.g., critical bug for validators, minor UX issue, etc.
-->

## Related

<!--
Link related issues or PRs.
-->

## Checklist

- [ ] Linked to a GitHub Issue (or this is the Issue)
- [ ] Repro steps included (for bugs)
- [ ] Impact described
- [ ] I understand minor typo/style doc fixes will not be accepted
