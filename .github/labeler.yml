proto:
  - changed-files:
      - any-glob-to-any-file: [
        "proto/**/*",
        "**/*.pb.go",
        "**/*.pb.gw.go"
      ]
types:
  - changed-files:
      - any-glob-to-any-file: [
        "types/**/*",
      ]
build:
  - changed-files:
      - any-glob-to-any-file: [
        "Makefile",
        "Dockerfile",
        "docker-compose.yml",
        "scripts/*",
        "config.yml",
      ]
CI:
  - changed-files:
      - any-glob-to-any-file: [
        ".github/**/*",
        ".mergify.yml",
        ".golangci.yml",
        "buf.yaml",
      ]
CLI:
  - changed-files:
      - any-glob-to-any-file: [
        "client/**/*",
        "x/*/client/**/*",
      ]
tests:
  - changed-files:
      - any-glob-to-any-file: [
        "tests/**/*",
        "testutil/**/*",
        "**/*_test.go",
      ]
contracts:
  - changed-files:
      - any-glob-to-any-file: [
        "contracts/**/*",
        "*.sol",
      ]
precompile:
  - changed-files:
      - any-glob-to-any-file: [
        "precompiles/**/*",
      ]
release:
  - base-branch: "^release/"
feature:
  - head-branch: "^feat/"
