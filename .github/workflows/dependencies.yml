name: "Dependency Review"
on:
  workflow_call:
  workflow_dispatch:
  schedule:
    # run every day at 3:00 UTC
    - cron: "0 3 * * *"

permissions: read-all

jobs:
  dependency-review:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/setup-go@v5
        with:
          go-version: '1.23.6'
          check-latest: true
      - name: "Checkout Repository"
        uses: actions/checkout@v4
      - uses: technote-space/get-diff-action@v6.1.2
        with:
          PATTERNS: |
            **/**.go
            go.mod
            go.sum
            *.toml
      - name: "Dependency Review"
        uses: actions/dependency-review-action@v4
      - name: "Get govulncheck"
        run: go install golang.org/x/vuln/cmd/govulncheck@latest
      - name: Run govulncheck
        run: govulncheck -show verbose ./...
        shell: bash
