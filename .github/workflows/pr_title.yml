name: PR Conventional Commit Validation

on:
  pull_request:
    types: [opened, synchronize, reopened, edited]

jobs:
  validate-pr-title:
    runs-on: ubuntu-latest
    steps:
      - name: PR Conventional Commit Validation
        uses:  ytanikin/pr-conventional-commits@1.4.0
        with:
          task_types: '["feat","fix","docs","test","ci","refactor","perf","chore","revert","style","build"]'
          add_label: 'false'