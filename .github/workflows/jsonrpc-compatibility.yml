name: JSON-RPC Compatibility Test
on:
  pull_request:
    paths:
      - '.github/workflows/jsonrpc-compatibility.yml'
      - 'tests/jsonrpc/**'
      - 'rpc/**'
      - 'evmd/**'
      - '**/**.go'
      - 'go.mod'
      - 'go.sum'
      - 'evmd/go.mod'
      - 'evmd/go.sum'
  merge_group:
  push:
    branches:
      - main
      - release/**
permissions: read-all

jobs:
  cleanup-runs:
    runs-on: ubuntu-latest
    steps:
      - uses: rok<PERSON>kar/workflow-run-cleanup-action@master
        env:
          GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
    if: ${{ !startsWith(github.ref, 'refs/tags/') && github.ref != 'refs/heads/main' }}

  jsonrpc-compatibility-test:
    runs-on: depot-ubuntu-22.04-8
    timeout-minutes: 45
    steps:
      - uses: actions/setup-go@v5
        with:
          go-version: "1.22"
          check-latest: true
      
      - uses: actions/checkout@v4
      
      - uses: technote-space/get-diff-action@v6.1.2
        with:
          PATTERNS: |
            .github/workflows/jsonrpc-compatibility.yml
            tests/jsonrpc/**
            rpc/**
            evmd/**
            **/**.go
            go.mod
            go.sum
            evmd/go.mod
            evmd/go.sum
      
      - name: Set up Docker
        uses: docker/setup-buildx-action@v3
        if: ${{ env.GIT_DIFF }}
      
      - name: Run JSON-RPC compatibility tests
        run: |
          echo "🧪 Running JSON-RPC compatibility tests using containerized environment..."
          
          # Run the containerized test suite
          make test-rpc-compat > test_output.log 2>&1 || {
            echo "⚠️ Test exited with non-zero code, checking results..."
          }
          
          # Display the test output
          cat test_output.log
          
          # Check for failure criterion: count of "Failed" tests > 0
          failed_count=$(grep -o "Failed:.*[0-9]" test_output.log | grep -o "[0-9]\+" | head -1 || echo "0")
          
          echo "📊 Test Results Summary:"
          echo "  Failed tests: $failed_count"
          
          if [ "$failed_count" -gt 0 ]; then
            echo "❌ CI FAILURE: Found $failed_count failed tests"
            echo "   Criterion: CI fails when any test has status 'Failed' (not 'Not Implemented', 'Legacy', or 'Skipped')"
            exit 1
          else
            echo "✅ CI PASS: No failed tests found"
            echo "   Note: 'Not Implemented', 'Legacy', and 'Skipped' tests are acceptable and don't cause CI failure"
          fi
        if: ${{ env.GIT_DIFF }}
