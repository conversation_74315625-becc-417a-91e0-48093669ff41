name: <PERSON><PERSON>
# Lint runs golangci-lint over the entire Cosmos EVM repository
# This workflow runs on every pull request and merge queue

on:
  pull_request:
  merge_group:

# cancel superseded runs
concurrency:
  group: lint-${{ github.ref }}-${{ github.event_name }}
  cancel-in-progress: true

permissions: read-all

jobs:
  golangci:
    name: Run golangci-lint
    runs-on: depot-ubuntu-24.04-8
    timeout-minutes: 15
    steps:
      - uses: actions/setup-go@v5
        with:
          go-version: "1.24"
          check-latest: true

      - uses: actions/checkout@v4

      - name: Cache Go modules
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-

      # Determine diff so we don't lint when no Go files are changed
      - uses: technote-space/get-diff-action@v6.1.2
        with:
          PATTERNS: |
            **/**.go
            go.mod
            go.sum
            *.toml

      - name: Run linting
        if: env.GIT_DIFF
        id: lint_long
        run: |
          make lint-go

  markdown-lint:
    name: markdownlint (cli2, minimal)
    runs-on: ubuntu-latest
    timeout-minutes: 10
    permissions:
      contents: read   # read-only permissions
    steps:
      # Standard checkout for all cases
      - name: Checkout
        uses: actions/checkout@v4

      # Only run if markdown or configs changed
      - uses: technote-space/get-diff-action@v6.1.2
        with:
          PATTERNS: |
            **/*.md
            .markdownlint.yml
            .markdownlintignore

      # Stable Node
      - uses: actions/setup-node@v4
        if: env.GIT_DIFF
        with:
          node-version: "20"

      # Lint: check only, fail on errors
      - name: markdownlint (check only)
        id: md_lint
        if: env.GIT_DIFF
        uses: DavidAnson/markdownlint-cli2-action@v16
        with:
          globs: "**/*.md"
          config: .markdownlint.yml
          fix: false
