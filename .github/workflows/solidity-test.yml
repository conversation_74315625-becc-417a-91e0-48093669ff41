name: Solidity Test
on:
  merge_group:
  pull_request:
  push:
    branches:
      - main
      - release/**
permissions: read-all

jobs:
  test-solidity:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/setup-go@v5
        with:
          go-version: '1.22'
          check-latest: true
      - uses: actions/checkout@v4
      - uses: technote-space/get-diff-action@v6.1.2
        with:
          PATTERNS: |
            **/**.sol
            **/**.go
            go.mod
            go.sum
            *.toml
      - name: Test Solidity
        run: |
          make test-solidity
        if: env.GIT_DIFF
