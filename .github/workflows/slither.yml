name: Slither Analysis

on:
  pull_request:
permissions: read-all

jobs:
  analyze:
    name: Run Slither
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Get Diff
        uses: technote-space/get-diff-action@v6.1.2
        with:
          PATTERNS: |
            **/*.sol
      - name: Node dependencies Install
        run: |
          cd contracts && npm i
          cp -r node_modules/@openzeppelin .
      - name: Run Slither Action
        uses: crytic/slither-action@v0.4.0
        continue-on-error: true
        id: slither
        with:
          target: contracts/
        if: "env.GIT_DIFF"