name: Tests
on:
  merge_group:
  pull_request:
  push:
    branches:
      - main
      - release/**
permissions: read-all

jobs:
  cleanup-runs:
    runs-on: ubuntu-latest
    steps:
      - uses: r<PERSON><PERSON><PERSON>/workflow-run-cleanup-action@master
        env:
          GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
    if: "!startsWith(github.ref, 'refs/tags/') && github.ref != 'refs/heads/main'"

  test-unit-cover:
    runs-on: depot-ubuntu-24.04-16
    steps:
      - uses: actions/setup-go@v5
        with:
          go-version: "1.22"
          check-latest: true
      - uses: actions/checkout@v4
      - uses: technote-space/get-diff-action@v6.1.2
        with:
          PATTERNS: |
            .github/workflows/test.yml
            **/**.sol
            **/**.go
            go.mod
            go.sum
            evmd/go.mod
            evmd/go.sum
            evmd/**/**.go
            *.toml
      - name: Test and Create Coverage Report
        run: |
          make test-unit-cover
        if: env.GIT_DIFF
      - uses: codecov/codecov-action@v5
        with:
          file: ./coverage.txt
          # We were getting a 500 error on codecov servers
          # so we decided to avoid blocking the CI if this fails
          fail_ci_if_error: false
        if: env.GIT_DIFF
        env:
          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}

  test-scripts:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: technote-space/get-diff-action@v6.1.2
        with:
          PATTERNS: |
            .github/workflows/test.yml
            ./scripts/**
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"
        if: env.GIT_DIFF
      - name: Install Pytest
        run: |
          python -m pip install --upgrade pip
          pip install pytest
        if: env.GIT_DIFF
      - name: Test Scripts
        run: |
          make test-scripts
        if: env.GIT_DIFF

  test-fuzz:
    runs-on: depot-ubuntu-24.04-4
    steps:
      - uses: actions/setup-go@v5
        with:
          go-version: "1.22"
          check-latest: true
      - uses: actions/checkout@v4
      - uses: technote-space/get-diff-action@v6.1.2
        with:
          PATTERNS: |
            .github/workflows/test.yml
            **/**.sol
            **/**.go
            go.mod
            go.sum
            *.toml
      - name: run fuzz tests
        run: |
          make test-fuzz
        if: env.GIT_DIFF
