name: <PERSON><PERSON><PERSON>
# This workflow is only run when a .sol file has been changed
on:
  pull_request:

permissions: read-all

jobs:
  solhint:
    name: runner / solhint
    runs-on: ubuntu-latest 
    steps:
      - uses: actions/checkout@v4
      - uses: technote-space/get-diff-action@v6.1.2
        id: git_diff
        with:
          PATTERNS: |
            **/**.sol
      - uses: actions/setup-node@v4
        if: env.GIT_DIFF
      - run: npm install -g solhint@v5.0.5
        if: env.GIT_DIFF
      - run: solhint --version
        if: env.GIT_DIFF
      - run: solhint '**/*.sol'
        if: env.GIT_DIFF
