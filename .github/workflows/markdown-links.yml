name: Check Markdown links
on: 
  pull_request:
    branches:
      - main
      - release/**
permissions: read-all

jobs:
  markdown-link-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: technote-space/get-diff-action@v6.1.2
        with:
          PATTERNS: |
            **.md
      - uses: gaurav-nelson/github-action-markdown-link-check@master
        with:
          check-modified-files-only: "yes"
          use-quiet-mode: "yes"
          base-branch: "main"
          config-file: "mlc_config.json"
        if: env.GIT_DIFF
