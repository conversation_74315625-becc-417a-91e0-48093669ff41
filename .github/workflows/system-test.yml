name: System Test
on:
  merge_group:
  pull_request:
  push:
    branches:
      - main
      - release/**
permissions: read-all

jobs:
  cleanup-runs:
    runs-on: ubuntu-latest
    steps:
      - uses: r<PERSON><PERSON><PERSON>/workflow-run-cleanup-action@master
        env:
          GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
    if: "!startsWith(github.ref, 'refs/tags/') && github.ref != 'refs/heads/main'"

  test-system:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/setup-go@v5
        with:
          go-version: "1.22"
          check-latest: true
      - name: Install Foundry (forge/cast/anvil)
        uses: foundry-rs/foundry-toolchain@v1
        with:
          version: stable
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true
      - uses: technote-space/get-diff-action@v6.1.2
        with:
          PATTERNS: |
            .github/workflows/test-system.yml
            **/**.sol
            **/**.go
            go.mod
            go.sum
            *.toml
      - name: run system tests
        run: |
          make test-system
        if: env.GIT_DIFF
