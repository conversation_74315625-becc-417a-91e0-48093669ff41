name: Push to Buf Schema Registry
# This workflow runs when a new version tag is pushed to the repository.
# It then pushes the Protobuf files corresponding to that tag on to the
# Buf Schema Registry at https://buf.build/cosmos/evm
on:
  push:
    tags:
      - "v*.*.*"
permissions: read-all

jobs:
  push:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: bufbuild/buf-setup-action@v1.50.0
      # Push Cosmos EVM protos to the Buf Schema Registry
      - uses: bufbuild/buf-push-action@v1.2.0
        with:
          input: ./proto
          buf_token: ${{ secrets.BUF_TOKEN }}
