{"_format": "hh-sol-artifact-1", "contractName": "IBank", "sourceName": "solidity/precompiles/bank/IBank.sol", "abi": [{"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balances", "outputs": [{"components": [{"internalType": "address", "name": "contractAddress", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "internalType": "struct Balance[]", "name": "balances", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "erc20Address", "type": "address"}], "name": "supplyOf", "outputs": [{"internalType": "uint256", "name": "totalSupply", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"components": [{"internalType": "address", "name": "contractAddress", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "internalType": "struct Balance[]", "name": "totalSupply", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}