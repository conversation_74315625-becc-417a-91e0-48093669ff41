{"_format": "hh-sol-artifact-1", "contractName": "BankCaller", "sourceName": "solidity/precompiles/bank/testdata/BankCaller.sol", "abi": [{"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "callBalances", "outputs": [{"components": [{"internalType": "address", "name": "contractAddress", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "internalType": "struct Balance[]", "name": "balances", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "erc20Address", "type": "address"}], "name": "callSupplyOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "callTotalSupply", "outputs": [{"components": [{"internalType": "address", "name": "contractAddress", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "internalType": "struct Balance[]", "name": "totalSupply", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}