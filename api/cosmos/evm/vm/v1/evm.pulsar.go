// Code generated by protoc-gen-go-pulsar. DO NOT EDIT.
package vmv1

import (
	_ "cosmossdk.io/api/amino"
	fmt "fmt"
	runtime "github.com/cosmos/cosmos-proto/runtime"
	_ "github.com/cosmos/gogoproto/gogoproto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoiface "google.golang.org/protobuf/runtime/protoiface"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	io "io"
	reflect "reflect"
	sync "sync"
)

var _ protoreflect.List = (*_Params_4_list)(nil)

type _Params_4_list struct {
	list *[]int64
}

func (x *_Params_4_list) Len() int {
	if x.list == nil {
		return 0
	}
	return len(*x.list)
}

func (x *_Params_4_list) Get(i int) protoreflect.Value {
	return protoreflect.ValueOfInt64((*x.list)[i])
}

func (x *_Params_4_list) Set(i int, value protoreflect.Value) {
	valueUnwrapped := value.Int()
	concreteValue := valueUnwrapped
	(*x.list)[i] = concreteValue
}

func (x *_Params_4_list) Append(value protoreflect.Value) {
	valueUnwrapped := value.Int()
	concreteValue := valueUnwrapped
	*x.list = append(*x.list, concreteValue)
}

func (x *_Params_4_list) AppendMutable() protoreflect.Value {
	panic(fmt.Errorf("AppendMutable can not be called on message Params at list field ExtraEips as it is not of Message kind"))
}

func (x *_Params_4_list) Truncate(n int) {
	*x.list = (*x.list)[:n]
}

func (x *_Params_4_list) NewElement() protoreflect.Value {
	v := int64(0)
	return protoreflect.ValueOfInt64(v)
}

func (x *_Params_4_list) IsValid() bool {
	return x.list != nil
}

var _ protoreflect.List = (*_Params_7_list)(nil)

type _Params_7_list struct {
	list *[]string
}

func (x *_Params_7_list) Len() int {
	if x.list == nil {
		return 0
	}
	return len(*x.list)
}

func (x *_Params_7_list) Get(i int) protoreflect.Value {
	return protoreflect.ValueOfString((*x.list)[i])
}

func (x *_Params_7_list) Set(i int, value protoreflect.Value) {
	valueUnwrapped := value.String()
	concreteValue := valueUnwrapped
	(*x.list)[i] = concreteValue
}

func (x *_Params_7_list) Append(value protoreflect.Value) {
	valueUnwrapped := value.String()
	concreteValue := valueUnwrapped
	*x.list = append(*x.list, concreteValue)
}

func (x *_Params_7_list) AppendMutable() protoreflect.Value {
	panic(fmt.Errorf("AppendMutable can not be called on message Params at list field EvmChannels as it is not of Message kind"))
}

func (x *_Params_7_list) Truncate(n int) {
	*x.list = (*x.list)[:n]
}

func (x *_Params_7_list) NewElement() protoreflect.Value {
	v := ""
	return protoreflect.ValueOfString(v)
}

func (x *_Params_7_list) IsValid() bool {
	return x.list != nil
}

var _ protoreflect.List = (*_Params_9_list)(nil)

type _Params_9_list struct {
	list *[]string
}

func (x *_Params_9_list) Len() int {
	if x.list == nil {
		return 0
	}
	return len(*x.list)
}

func (x *_Params_9_list) Get(i int) protoreflect.Value {
	return protoreflect.ValueOfString((*x.list)[i])
}

func (x *_Params_9_list) Set(i int, value protoreflect.Value) {
	valueUnwrapped := value.String()
	concreteValue := valueUnwrapped
	(*x.list)[i] = concreteValue
}

func (x *_Params_9_list) Append(value protoreflect.Value) {
	valueUnwrapped := value.String()
	concreteValue := valueUnwrapped
	*x.list = append(*x.list, concreteValue)
}

func (x *_Params_9_list) AppendMutable() protoreflect.Value {
	panic(fmt.Errorf("AppendMutable can not be called on message Params at list field ActiveStaticPrecompiles as it is not of Message kind"))
}

func (x *_Params_9_list) Truncate(n int) {
	*x.list = (*x.list)[:n]
}

func (x *_Params_9_list) NewElement() protoreflect.Value {
	v := ""
	return protoreflect.ValueOfString(v)
}

func (x *_Params_9_list) IsValid() bool {
	return x.list != nil
}

var (
	md_Params                           protoreflect.MessageDescriptor
	fd_Params_evm_denom                 protoreflect.FieldDescriptor
	fd_Params_extra_eips                protoreflect.FieldDescriptor
	fd_Params_evm_channels              protoreflect.FieldDescriptor
	fd_Params_access_control            protoreflect.FieldDescriptor
	fd_Params_active_static_precompiles protoreflect.FieldDescriptor
	fd_Params_history_serve_window      protoreflect.FieldDescriptor
)

func init() {
	file_cosmos_evm_vm_v1_evm_proto_init()
	md_Params = File_cosmos_evm_vm_v1_evm_proto.Messages().ByName("Params")
	fd_Params_evm_denom = md_Params.Fields().ByName("evm_denom")
	fd_Params_extra_eips = md_Params.Fields().ByName("extra_eips")
	fd_Params_evm_channels = md_Params.Fields().ByName("evm_channels")
	fd_Params_access_control = md_Params.Fields().ByName("access_control")
	fd_Params_active_static_precompiles = md_Params.Fields().ByName("active_static_precompiles")
	fd_Params_history_serve_window = md_Params.Fields().ByName("history_serve_window")
}

var _ protoreflect.Message = (*fastReflection_Params)(nil)

type fastReflection_Params Params

func (x *Params) ProtoReflect() protoreflect.Message {
	return (*fastReflection_Params)(x)
}

func (x *Params) slowProtoReflect() protoreflect.Message {
	mi := &file_cosmos_evm_vm_v1_evm_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

var _fastReflection_Params_messageType fastReflection_Params_messageType
var _ protoreflect.MessageType = fastReflection_Params_messageType{}

type fastReflection_Params_messageType struct{}

func (x fastReflection_Params_messageType) Zero() protoreflect.Message {
	return (*fastReflection_Params)(nil)
}
func (x fastReflection_Params_messageType) New() protoreflect.Message {
	return new(fastReflection_Params)
}
func (x fastReflection_Params_messageType) Descriptor() protoreflect.MessageDescriptor {
	return md_Params
}

// Descriptor returns message descriptor, which contains only the protobuf
// type information for the message.
func (x *fastReflection_Params) Descriptor() protoreflect.MessageDescriptor {
	return md_Params
}

// Type returns the message type, which encapsulates both Go and protobuf
// type information. If the Go type information is not needed,
// it is recommended that the message descriptor be used instead.
func (x *fastReflection_Params) Type() protoreflect.MessageType {
	return _fastReflection_Params_messageType
}

// New returns a newly allocated and mutable empty message.
func (x *fastReflection_Params) New() protoreflect.Message {
	return new(fastReflection_Params)
}

// Interface unwraps the message reflection interface and
// returns the underlying ProtoMessage interface.
func (x *fastReflection_Params) Interface() protoreflect.ProtoMessage {
	return (*Params)(x)
}

// Range iterates over every populated field in an undefined order,
// calling f for each field descriptor and value encountered.
// Range returns immediately if f returns false.
// While iterating, mutating operations may only be performed
// on the current field descriptor.
func (x *fastReflection_Params) Range(f func(protoreflect.FieldDescriptor, protoreflect.Value) bool) {
	if x.EvmDenom != "" {
		value := protoreflect.ValueOfString(x.EvmDenom)
		if !f(fd_Params_evm_denom, value) {
			return
		}
	}
	if len(x.ExtraEips) != 0 {
		value := protoreflect.ValueOfList(&_Params_4_list{list: &x.ExtraEips})
		if !f(fd_Params_extra_eips, value) {
			return
		}
	}
	if len(x.EvmChannels) != 0 {
		value := protoreflect.ValueOfList(&_Params_7_list{list: &x.EvmChannels})
		if !f(fd_Params_evm_channels, value) {
			return
		}
	}
	if x.AccessControl != nil {
		value := protoreflect.ValueOfMessage(x.AccessControl.ProtoReflect())
		if !f(fd_Params_access_control, value) {
			return
		}
	}
	if len(x.ActiveStaticPrecompiles) != 0 {
		value := protoreflect.ValueOfList(&_Params_9_list{list: &x.ActiveStaticPrecompiles})
		if !f(fd_Params_active_static_precompiles, value) {
			return
		}
	}
	if x.HistoryServeWindow != uint64(0) {
		value := protoreflect.ValueOfUint64(x.HistoryServeWindow)
		if !f(fd_Params_history_serve_window, value) {
			return
		}
	}
}

// Has reports whether a field is populated.
//
// Some fields have the property of nullability where it is possible to
// distinguish between the default value of a field and whether the field
// was explicitly populated with the default value. Singular message fields,
// member fields of a oneof, and proto2 scalar fields are nullable. Such
// fields are populated only if explicitly set.
//
// In other cases (aside from the nullable cases above),
// a proto3 scalar field is populated if it contains a non-zero value, and
// a repeated field is populated if it is non-empty.
func (x *fastReflection_Params) Has(fd protoreflect.FieldDescriptor) bool {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.Params.evm_denom":
		return x.EvmDenom != ""
	case "cosmos.evm.vm.v1.Params.extra_eips":
		return len(x.ExtraEips) != 0
	case "cosmos.evm.vm.v1.Params.evm_channels":
		return len(x.EvmChannels) != 0
	case "cosmos.evm.vm.v1.Params.access_control":
		return x.AccessControl != nil
	case "cosmos.evm.vm.v1.Params.active_static_precompiles":
		return len(x.ActiveStaticPrecompiles) != 0
	case "cosmos.evm.vm.v1.Params.history_serve_window":
		return x.HistoryServeWindow != uint64(0)
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.Params"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.Params does not contain field %s", fd.FullName()))
	}
}

// Clear clears the field such that a subsequent Has call reports false.
//
// Clearing an extension field clears both the extension type and value
// associated with the given field number.
//
// Clear is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_Params) Clear(fd protoreflect.FieldDescriptor) {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.Params.evm_denom":
		x.EvmDenom = ""
	case "cosmos.evm.vm.v1.Params.extra_eips":
		x.ExtraEips = nil
	case "cosmos.evm.vm.v1.Params.evm_channels":
		x.EvmChannels = nil
	case "cosmos.evm.vm.v1.Params.access_control":
		x.AccessControl = nil
	case "cosmos.evm.vm.v1.Params.active_static_precompiles":
		x.ActiveStaticPrecompiles = nil
	case "cosmos.evm.vm.v1.Params.history_serve_window":
		x.HistoryServeWindow = uint64(0)
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.Params"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.Params does not contain field %s", fd.FullName()))
	}
}

// Get retrieves the value for a field.
//
// For unpopulated scalars, it returns the default value, where
// the default value of a bytes scalar is guaranteed to be a copy.
// For unpopulated composite types, it returns an empty, read-only view
// of the value; to obtain a mutable reference, use Mutable.
func (x *fastReflection_Params) Get(descriptor protoreflect.FieldDescriptor) protoreflect.Value {
	switch descriptor.FullName() {
	case "cosmos.evm.vm.v1.Params.evm_denom":
		value := x.EvmDenom
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.Params.extra_eips":
		if len(x.ExtraEips) == 0 {
			return protoreflect.ValueOfList(&_Params_4_list{})
		}
		listValue := &_Params_4_list{list: &x.ExtraEips}
		return protoreflect.ValueOfList(listValue)
	case "cosmos.evm.vm.v1.Params.evm_channels":
		if len(x.EvmChannels) == 0 {
			return protoreflect.ValueOfList(&_Params_7_list{})
		}
		listValue := &_Params_7_list{list: &x.EvmChannels}
		return protoreflect.ValueOfList(listValue)
	case "cosmos.evm.vm.v1.Params.access_control":
		value := x.AccessControl
		return protoreflect.ValueOfMessage(value.ProtoReflect())
	case "cosmos.evm.vm.v1.Params.active_static_precompiles":
		if len(x.ActiveStaticPrecompiles) == 0 {
			return protoreflect.ValueOfList(&_Params_9_list{})
		}
		listValue := &_Params_9_list{list: &x.ActiveStaticPrecompiles}
		return protoreflect.ValueOfList(listValue)
	case "cosmos.evm.vm.v1.Params.history_serve_window":
		value := x.HistoryServeWindow
		return protoreflect.ValueOfUint64(value)
	default:
		if descriptor.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.Params"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.Params does not contain field %s", descriptor.FullName()))
	}
}

// Set stores the value for a field.
//
// For a field belonging to a oneof, it implicitly clears any other field
// that may be currently set within the same oneof.
// For extension fields, it implicitly stores the provided ExtensionType.
// When setting a composite type, it is unspecified whether the stored value
// aliases the source's memory in any way. If the composite value is an
// empty, read-only value, then it panics.
//
// Set is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_Params) Set(fd protoreflect.FieldDescriptor, value protoreflect.Value) {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.Params.evm_denom":
		x.EvmDenom = value.Interface().(string)
	case "cosmos.evm.vm.v1.Params.extra_eips":
		lv := value.List()
		clv := lv.(*_Params_4_list)
		x.ExtraEips = *clv.list
	case "cosmos.evm.vm.v1.Params.evm_channels":
		lv := value.List()
		clv := lv.(*_Params_7_list)
		x.EvmChannels = *clv.list
	case "cosmos.evm.vm.v1.Params.access_control":
		x.AccessControl = value.Message().Interface().(*AccessControl)
	case "cosmos.evm.vm.v1.Params.active_static_precompiles":
		lv := value.List()
		clv := lv.(*_Params_9_list)
		x.ActiveStaticPrecompiles = *clv.list
	case "cosmos.evm.vm.v1.Params.history_serve_window":
		x.HistoryServeWindow = value.Uint()
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.Params"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.Params does not contain field %s", fd.FullName()))
	}
}

// Mutable returns a mutable reference to a composite type.
//
// If the field is unpopulated, it may allocate a composite value.
// For a field belonging to a oneof, it implicitly clears any other field
// that may be currently set within the same oneof.
// For extension fields, it implicitly stores the provided ExtensionType
// if not already stored.
// It panics if the field does not contain a composite type.
//
// Mutable is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_Params) Mutable(fd protoreflect.FieldDescriptor) protoreflect.Value {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.Params.extra_eips":
		if x.ExtraEips == nil {
			x.ExtraEips = []int64{}
		}
		value := &_Params_4_list{list: &x.ExtraEips}
		return protoreflect.ValueOfList(value)
	case "cosmos.evm.vm.v1.Params.evm_channels":
		if x.EvmChannels == nil {
			x.EvmChannels = []string{}
		}
		value := &_Params_7_list{list: &x.EvmChannels}
		return protoreflect.ValueOfList(value)
	case "cosmos.evm.vm.v1.Params.access_control":
		if x.AccessControl == nil {
			x.AccessControl = new(AccessControl)
		}
		return protoreflect.ValueOfMessage(x.AccessControl.ProtoReflect())
	case "cosmos.evm.vm.v1.Params.active_static_precompiles":
		if x.ActiveStaticPrecompiles == nil {
			x.ActiveStaticPrecompiles = []string{}
		}
		value := &_Params_9_list{list: &x.ActiveStaticPrecompiles}
		return protoreflect.ValueOfList(value)
	case "cosmos.evm.vm.v1.Params.evm_denom":
		panic(fmt.Errorf("field evm_denom of message cosmos.evm.vm.v1.Params is not mutable"))
	case "cosmos.evm.vm.v1.Params.history_serve_window":
		panic(fmt.Errorf("field history_serve_window of message cosmos.evm.vm.v1.Params is not mutable"))
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.Params"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.Params does not contain field %s", fd.FullName()))
	}
}

// NewField returns a new value that is assignable to the field
// for the given descriptor. For scalars, this returns the default value.
// For lists, maps, and messages, this returns a new, empty, mutable value.
func (x *fastReflection_Params) NewField(fd protoreflect.FieldDescriptor) protoreflect.Value {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.Params.evm_denom":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.Params.extra_eips":
		list := []int64{}
		return protoreflect.ValueOfList(&_Params_4_list{list: &list})
	case "cosmos.evm.vm.v1.Params.evm_channels":
		list := []string{}
		return protoreflect.ValueOfList(&_Params_7_list{list: &list})
	case "cosmos.evm.vm.v1.Params.access_control":
		m := new(AccessControl)
		return protoreflect.ValueOfMessage(m.ProtoReflect())
	case "cosmos.evm.vm.v1.Params.active_static_precompiles":
		list := []string{}
		return protoreflect.ValueOfList(&_Params_9_list{list: &list})
	case "cosmos.evm.vm.v1.Params.history_serve_window":
		return protoreflect.ValueOfUint64(uint64(0))
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.Params"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.Params does not contain field %s", fd.FullName()))
	}
}

// WhichOneof reports which field within the oneof is populated,
// returning nil if none are populated.
// It panics if the oneof descriptor does not belong to this message.
func (x *fastReflection_Params) WhichOneof(d protoreflect.OneofDescriptor) protoreflect.FieldDescriptor {
	switch d.FullName() {
	default:
		panic(fmt.Errorf("%s is not a oneof field in cosmos.evm.vm.v1.Params", d.FullName()))
	}
	panic("unreachable")
}

// GetUnknown retrieves the entire list of unknown fields.
// The caller may only mutate the contents of the RawFields
// if the mutated bytes are stored back into the message with SetUnknown.
func (x *fastReflection_Params) GetUnknown() protoreflect.RawFields {
	return x.unknownFields
}

// SetUnknown stores an entire list of unknown fields.
// The raw fields must be syntactically valid according to the wire format.
// An implementation may panic if this is not the case.
// Once stored, the caller must not mutate the content of the RawFields.
// An empty RawFields may be passed to clear the fields.
//
// SetUnknown is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_Params) SetUnknown(fields protoreflect.RawFields) {
	x.unknownFields = fields
}

// IsValid reports whether the message is valid.
//
// An invalid message is an empty, read-only value.
//
// An invalid message often corresponds to a nil pointer of the concrete
// message type, but the details are implementation dependent.
// Validity is not part of the protobuf data model, and may not
// be preserved in marshaling or other operations.
func (x *fastReflection_Params) IsValid() bool {
	return x != nil
}

// ProtoMethods returns optional fastReflectionFeature-path implementations of various operations.
// This method may return nil.
//
// The returned methods type is identical to
// "google.golang.org/protobuf/runtime/protoiface".Methods.
// Consult the protoiface package documentation for details.
func (x *fastReflection_Params) ProtoMethods() *protoiface.Methods {
	size := func(input protoiface.SizeInput) protoiface.SizeOutput {
		x := input.Message.Interface().(*Params)
		if x == nil {
			return protoiface.SizeOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Size:              0,
			}
		}
		options := runtime.SizeInputToOptions(input)
		_ = options
		var n int
		var l int
		_ = l
		l = len(x.EvmDenom)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		if len(x.ExtraEips) > 0 {
			l = 0
			for _, e := range x.ExtraEips {
				l += runtime.Sov(uint64(e))
			}
			n += 1 + runtime.Sov(uint64(l)) + l
		}
		if len(x.EvmChannels) > 0 {
			for _, s := range x.EvmChannels {
				l = len(s)
				n += 1 + l + runtime.Sov(uint64(l))
			}
		}
		if x.AccessControl != nil {
			l = options.Size(x.AccessControl)
			n += 1 + l + runtime.Sov(uint64(l))
		}
		if len(x.ActiveStaticPrecompiles) > 0 {
			for _, s := range x.ActiveStaticPrecompiles {
				l = len(s)
				n += 1 + l + runtime.Sov(uint64(l))
			}
		}
		if x.HistoryServeWindow != 0 {
			n += 1 + runtime.Sov(uint64(x.HistoryServeWindow))
		}
		if x.unknownFields != nil {
			n += len(x.unknownFields)
		}
		return protoiface.SizeOutput{
			NoUnkeyedLiterals: input.NoUnkeyedLiterals,
			Size:              n,
		}
	}

	marshal := func(input protoiface.MarshalInput) (protoiface.MarshalOutput, error) {
		x := input.Message.Interface().(*Params)
		if x == nil {
			return protoiface.MarshalOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Buf:               input.Buf,
			}, nil
		}
		options := runtime.MarshalInputToOptions(input)
		_ = options
		size := options.Size(x)
		dAtA := make([]byte, size)
		i := len(dAtA)
		_ = i
		var l int
		_ = l
		if x.unknownFields != nil {
			i -= len(x.unknownFields)
			copy(dAtA[i:], x.unknownFields)
		}
		if x.HistoryServeWindow != 0 {
			i = runtime.EncodeVarint(dAtA, i, uint64(x.HistoryServeWindow))
			i--
			dAtA[i] = 0x50
		}
		if len(x.ActiveStaticPrecompiles) > 0 {
			for iNdEx := len(x.ActiveStaticPrecompiles) - 1; iNdEx >= 0; iNdEx-- {
				i -= len(x.ActiveStaticPrecompiles[iNdEx])
				copy(dAtA[i:], x.ActiveStaticPrecompiles[iNdEx])
				i = runtime.EncodeVarint(dAtA, i, uint64(len(x.ActiveStaticPrecompiles[iNdEx])))
				i--
				dAtA[i] = 0x4a
			}
		}
		if x.AccessControl != nil {
			encoded, err := options.Marshal(x.AccessControl)
			if err != nil {
				return protoiface.MarshalOutput{
					NoUnkeyedLiterals: input.NoUnkeyedLiterals,
					Buf:               input.Buf,
				}, err
			}
			i -= len(encoded)
			copy(dAtA[i:], encoded)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(encoded)))
			i--
			dAtA[i] = 0x42
		}
		if len(x.EvmChannels) > 0 {
			for iNdEx := len(x.EvmChannels) - 1; iNdEx >= 0; iNdEx-- {
				i -= len(x.EvmChannels[iNdEx])
				copy(dAtA[i:], x.EvmChannels[iNdEx])
				i = runtime.EncodeVarint(dAtA, i, uint64(len(x.EvmChannels[iNdEx])))
				i--
				dAtA[i] = 0x3a
			}
		}
		if len(x.ExtraEips) > 0 {
			var pksize2 int
			for _, num := range x.ExtraEips {
				pksize2 += runtime.Sov(uint64(num))
			}
			i -= pksize2
			j1 := i
			for _, num1 := range x.ExtraEips {
				num := uint64(num1)
				for num >= 1<<7 {
					dAtA[j1] = uint8(uint64(num)&0x7f | 0x80)
					num >>= 7
					j1++
				}
				dAtA[j1] = uint8(num)
				j1++
			}
			i = runtime.EncodeVarint(dAtA, i, uint64(pksize2))
			i--
			dAtA[i] = 0x22
		}
		if len(x.EvmDenom) > 0 {
			i -= len(x.EvmDenom)
			copy(dAtA[i:], x.EvmDenom)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.EvmDenom)))
			i--
			dAtA[i] = 0xa
		}
		if input.Buf != nil {
			input.Buf = append(input.Buf, dAtA...)
		} else {
			input.Buf = dAtA
		}
		return protoiface.MarshalOutput{
			NoUnkeyedLiterals: input.NoUnkeyedLiterals,
			Buf:               input.Buf,
		}, nil
	}
	unmarshal := func(input protoiface.UnmarshalInput) (protoiface.UnmarshalOutput, error) {
		x := input.Message.Interface().(*Params)
		if x == nil {
			return protoiface.UnmarshalOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Flags:             input.Flags,
			}, nil
		}
		options := runtime.UnmarshalInputToOptions(input)
		_ = options
		dAtA := input.Buf
		l := len(dAtA)
		iNdEx := 0
		for iNdEx < l {
			preIndex := iNdEx
			var wire uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
				}
				if iNdEx >= l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				wire |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			fieldNum := int32(wire >> 3)
			wireType := int(wire & 0x7)
			if wireType == 4 {
				return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: Params: wiretype end group for non-group")
			}
			if fieldNum <= 0 {
				return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: Params: illegal tag %d (wire type %d)", fieldNum, wire)
			}
			switch fieldNum {
			case 1:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field EvmDenom", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.EvmDenom = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 4:
				if wireType == 0 {
					var v int64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
						}
						if iNdEx >= l {
							return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					x.ExtraEips = append(x.ExtraEips, v)
				} else if wireType == 2 {
					var packedLen int
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
						}
						if iNdEx >= l {
							return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						packedLen |= int(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					if packedLen < 0 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
					}
					postIndex := iNdEx + packedLen
					if postIndex < 0 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
					}
					if postIndex > l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					var elementCount int
					var count int
					for _, integer := range dAtA[iNdEx:postIndex] {
						if integer < 128 {
							count++
						}
					}
					elementCount = count
					if elementCount != 0 && len(x.ExtraEips) == 0 {
						x.ExtraEips = make([]int64, 0, elementCount)
					}
					for iNdEx < postIndex {
						var v int64
						for shift := uint(0); ; shift += 7 {
							if shift >= 64 {
								return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
							}
							if iNdEx >= l {
								return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
							}
							b := dAtA[iNdEx]
							iNdEx++
							v |= int64(b&0x7F) << shift
							if b < 0x80 {
								break
							}
						}
						x.ExtraEips = append(x.ExtraEips, v)
					}
				} else {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field ExtraEips", wireType)
				}
			case 7:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field EvmChannels", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.EvmChannels = append(x.EvmChannels, string(dAtA[iNdEx:postIndex]))
				iNdEx = postIndex
			case 8:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field AccessControl", wireType)
				}
				var msglen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					msglen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if msglen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + msglen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				if x.AccessControl == nil {
					x.AccessControl = &AccessControl{}
				}
				if err := options.Unmarshal(dAtA[iNdEx:postIndex], x.AccessControl); err != nil {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, err
				}
				iNdEx = postIndex
			case 9:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field ActiveStaticPrecompiles", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.ActiveStaticPrecompiles = append(x.ActiveStaticPrecompiles, string(dAtA[iNdEx:postIndex]))
				iNdEx = postIndex
			case 10:
				if wireType != 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field HistoryServeWindow", wireType)
				}
				x.HistoryServeWindow = 0
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					x.HistoryServeWindow |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
			default:
				iNdEx = preIndex
				skippy, err := runtime.Skip(dAtA[iNdEx:])
				if err != nil {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, err
				}
				if (skippy < 0) || (iNdEx+skippy) < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if (iNdEx + skippy) > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				if !options.DiscardUnknown {
					x.unknownFields = append(x.unknownFields, dAtA[iNdEx:iNdEx+skippy]...)
				}
				iNdEx += skippy
			}
		}

		if iNdEx > l {
			return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
		}
		return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, nil
	}
	return &protoiface.Methods{
		NoUnkeyedLiterals: struct{}{},
		Flags:             protoiface.SupportMarshalDeterministic | protoiface.SupportUnmarshalDiscardUnknown,
		Size:              size,
		Marshal:           marshal,
		Unmarshal:         unmarshal,
		Merge:             nil,
		CheckInitialized:  nil,
	}
}

var (
	md_AccessControl        protoreflect.MessageDescriptor
	fd_AccessControl_create protoreflect.FieldDescriptor
	fd_AccessControl_call   protoreflect.FieldDescriptor
)

func init() {
	file_cosmos_evm_vm_v1_evm_proto_init()
	md_AccessControl = File_cosmos_evm_vm_v1_evm_proto.Messages().ByName("AccessControl")
	fd_AccessControl_create = md_AccessControl.Fields().ByName("create")
	fd_AccessControl_call = md_AccessControl.Fields().ByName("call")
}

var _ protoreflect.Message = (*fastReflection_AccessControl)(nil)

type fastReflection_AccessControl AccessControl

func (x *AccessControl) ProtoReflect() protoreflect.Message {
	return (*fastReflection_AccessControl)(x)
}

func (x *AccessControl) slowProtoReflect() protoreflect.Message {
	mi := &file_cosmos_evm_vm_v1_evm_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

var _fastReflection_AccessControl_messageType fastReflection_AccessControl_messageType
var _ protoreflect.MessageType = fastReflection_AccessControl_messageType{}

type fastReflection_AccessControl_messageType struct{}

func (x fastReflection_AccessControl_messageType) Zero() protoreflect.Message {
	return (*fastReflection_AccessControl)(nil)
}
func (x fastReflection_AccessControl_messageType) New() protoreflect.Message {
	return new(fastReflection_AccessControl)
}
func (x fastReflection_AccessControl_messageType) Descriptor() protoreflect.MessageDescriptor {
	return md_AccessControl
}

// Descriptor returns message descriptor, which contains only the protobuf
// type information for the message.
func (x *fastReflection_AccessControl) Descriptor() protoreflect.MessageDescriptor {
	return md_AccessControl
}

// Type returns the message type, which encapsulates both Go and protobuf
// type information. If the Go type information is not needed,
// it is recommended that the message descriptor be used instead.
func (x *fastReflection_AccessControl) Type() protoreflect.MessageType {
	return _fastReflection_AccessControl_messageType
}

// New returns a newly allocated and mutable empty message.
func (x *fastReflection_AccessControl) New() protoreflect.Message {
	return new(fastReflection_AccessControl)
}

// Interface unwraps the message reflection interface and
// returns the underlying ProtoMessage interface.
func (x *fastReflection_AccessControl) Interface() protoreflect.ProtoMessage {
	return (*AccessControl)(x)
}

// Range iterates over every populated field in an undefined order,
// calling f for each field descriptor and value encountered.
// Range returns immediately if f returns false.
// While iterating, mutating operations may only be performed
// on the current field descriptor.
func (x *fastReflection_AccessControl) Range(f func(protoreflect.FieldDescriptor, protoreflect.Value) bool) {
	if x.Create != nil {
		value := protoreflect.ValueOfMessage(x.Create.ProtoReflect())
		if !f(fd_AccessControl_create, value) {
			return
		}
	}
	if x.Call != nil {
		value := protoreflect.ValueOfMessage(x.Call.ProtoReflect())
		if !f(fd_AccessControl_call, value) {
			return
		}
	}
}

// Has reports whether a field is populated.
//
// Some fields have the property of nullability where it is possible to
// distinguish between the default value of a field and whether the field
// was explicitly populated with the default value. Singular message fields,
// member fields of a oneof, and proto2 scalar fields are nullable. Such
// fields are populated only if explicitly set.
//
// In other cases (aside from the nullable cases above),
// a proto3 scalar field is populated if it contains a non-zero value, and
// a repeated field is populated if it is non-empty.
func (x *fastReflection_AccessControl) Has(fd protoreflect.FieldDescriptor) bool {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.AccessControl.create":
		return x.Create != nil
	case "cosmos.evm.vm.v1.AccessControl.call":
		return x.Call != nil
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.AccessControl"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.AccessControl does not contain field %s", fd.FullName()))
	}
}

// Clear clears the field such that a subsequent Has call reports false.
//
// Clearing an extension field clears both the extension type and value
// associated with the given field number.
//
// Clear is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_AccessControl) Clear(fd protoreflect.FieldDescriptor) {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.AccessControl.create":
		x.Create = nil
	case "cosmos.evm.vm.v1.AccessControl.call":
		x.Call = nil
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.AccessControl"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.AccessControl does not contain field %s", fd.FullName()))
	}
}

// Get retrieves the value for a field.
//
// For unpopulated scalars, it returns the default value, where
// the default value of a bytes scalar is guaranteed to be a copy.
// For unpopulated composite types, it returns an empty, read-only view
// of the value; to obtain a mutable reference, use Mutable.
func (x *fastReflection_AccessControl) Get(descriptor protoreflect.FieldDescriptor) protoreflect.Value {
	switch descriptor.FullName() {
	case "cosmos.evm.vm.v1.AccessControl.create":
		value := x.Create
		return protoreflect.ValueOfMessage(value.ProtoReflect())
	case "cosmos.evm.vm.v1.AccessControl.call":
		value := x.Call
		return protoreflect.ValueOfMessage(value.ProtoReflect())
	default:
		if descriptor.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.AccessControl"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.AccessControl does not contain field %s", descriptor.FullName()))
	}
}

// Set stores the value for a field.
//
// For a field belonging to a oneof, it implicitly clears any other field
// that may be currently set within the same oneof.
// For extension fields, it implicitly stores the provided ExtensionType.
// When setting a composite type, it is unspecified whether the stored value
// aliases the source's memory in any way. If the composite value is an
// empty, read-only value, then it panics.
//
// Set is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_AccessControl) Set(fd protoreflect.FieldDescriptor, value protoreflect.Value) {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.AccessControl.create":
		x.Create = value.Message().Interface().(*AccessControlType)
	case "cosmos.evm.vm.v1.AccessControl.call":
		x.Call = value.Message().Interface().(*AccessControlType)
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.AccessControl"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.AccessControl does not contain field %s", fd.FullName()))
	}
}

// Mutable returns a mutable reference to a composite type.
//
// If the field is unpopulated, it may allocate a composite value.
// For a field belonging to a oneof, it implicitly clears any other field
// that may be currently set within the same oneof.
// For extension fields, it implicitly stores the provided ExtensionType
// if not already stored.
// It panics if the field does not contain a composite type.
//
// Mutable is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_AccessControl) Mutable(fd protoreflect.FieldDescriptor) protoreflect.Value {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.AccessControl.create":
		if x.Create == nil {
			x.Create = new(AccessControlType)
		}
		return protoreflect.ValueOfMessage(x.Create.ProtoReflect())
	case "cosmos.evm.vm.v1.AccessControl.call":
		if x.Call == nil {
			x.Call = new(AccessControlType)
		}
		return protoreflect.ValueOfMessage(x.Call.ProtoReflect())
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.AccessControl"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.AccessControl does not contain field %s", fd.FullName()))
	}
}

// NewField returns a new value that is assignable to the field
// for the given descriptor. For scalars, this returns the default value.
// For lists, maps, and messages, this returns a new, empty, mutable value.
func (x *fastReflection_AccessControl) NewField(fd protoreflect.FieldDescriptor) protoreflect.Value {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.AccessControl.create":
		m := new(AccessControlType)
		return protoreflect.ValueOfMessage(m.ProtoReflect())
	case "cosmos.evm.vm.v1.AccessControl.call":
		m := new(AccessControlType)
		return protoreflect.ValueOfMessage(m.ProtoReflect())
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.AccessControl"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.AccessControl does not contain field %s", fd.FullName()))
	}
}

// WhichOneof reports which field within the oneof is populated,
// returning nil if none are populated.
// It panics if the oneof descriptor does not belong to this message.
func (x *fastReflection_AccessControl) WhichOneof(d protoreflect.OneofDescriptor) protoreflect.FieldDescriptor {
	switch d.FullName() {
	default:
		panic(fmt.Errorf("%s is not a oneof field in cosmos.evm.vm.v1.AccessControl", d.FullName()))
	}
	panic("unreachable")
}

// GetUnknown retrieves the entire list of unknown fields.
// The caller may only mutate the contents of the RawFields
// if the mutated bytes are stored back into the message with SetUnknown.
func (x *fastReflection_AccessControl) GetUnknown() protoreflect.RawFields {
	return x.unknownFields
}

// SetUnknown stores an entire list of unknown fields.
// The raw fields must be syntactically valid according to the wire format.
// An implementation may panic if this is not the case.
// Once stored, the caller must not mutate the content of the RawFields.
// An empty RawFields may be passed to clear the fields.
//
// SetUnknown is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_AccessControl) SetUnknown(fields protoreflect.RawFields) {
	x.unknownFields = fields
}

// IsValid reports whether the message is valid.
//
// An invalid message is an empty, read-only value.
//
// An invalid message often corresponds to a nil pointer of the concrete
// message type, but the details are implementation dependent.
// Validity is not part of the protobuf data model, and may not
// be preserved in marshaling or other operations.
func (x *fastReflection_AccessControl) IsValid() bool {
	return x != nil
}

// ProtoMethods returns optional fastReflectionFeature-path implementations of various operations.
// This method may return nil.
//
// The returned methods type is identical to
// "google.golang.org/protobuf/runtime/protoiface".Methods.
// Consult the protoiface package documentation for details.
func (x *fastReflection_AccessControl) ProtoMethods() *protoiface.Methods {
	size := func(input protoiface.SizeInput) protoiface.SizeOutput {
		x := input.Message.Interface().(*AccessControl)
		if x == nil {
			return protoiface.SizeOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Size:              0,
			}
		}
		options := runtime.SizeInputToOptions(input)
		_ = options
		var n int
		var l int
		_ = l
		if x.Create != nil {
			l = options.Size(x.Create)
			n += 1 + l + runtime.Sov(uint64(l))
		}
		if x.Call != nil {
			l = options.Size(x.Call)
			n += 1 + l + runtime.Sov(uint64(l))
		}
		if x.unknownFields != nil {
			n += len(x.unknownFields)
		}
		return protoiface.SizeOutput{
			NoUnkeyedLiterals: input.NoUnkeyedLiterals,
			Size:              n,
		}
	}

	marshal := func(input protoiface.MarshalInput) (protoiface.MarshalOutput, error) {
		x := input.Message.Interface().(*AccessControl)
		if x == nil {
			return protoiface.MarshalOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Buf:               input.Buf,
			}, nil
		}
		options := runtime.MarshalInputToOptions(input)
		_ = options
		size := options.Size(x)
		dAtA := make([]byte, size)
		i := len(dAtA)
		_ = i
		var l int
		_ = l
		if x.unknownFields != nil {
			i -= len(x.unknownFields)
			copy(dAtA[i:], x.unknownFields)
		}
		if x.Call != nil {
			encoded, err := options.Marshal(x.Call)
			if err != nil {
				return protoiface.MarshalOutput{
					NoUnkeyedLiterals: input.NoUnkeyedLiterals,
					Buf:               input.Buf,
				}, err
			}
			i -= len(encoded)
			copy(dAtA[i:], encoded)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(encoded)))
			i--
			dAtA[i] = 0x12
		}
		if x.Create != nil {
			encoded, err := options.Marshal(x.Create)
			if err != nil {
				return protoiface.MarshalOutput{
					NoUnkeyedLiterals: input.NoUnkeyedLiterals,
					Buf:               input.Buf,
				}, err
			}
			i -= len(encoded)
			copy(dAtA[i:], encoded)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(encoded)))
			i--
			dAtA[i] = 0xa
		}
		if input.Buf != nil {
			input.Buf = append(input.Buf, dAtA...)
		} else {
			input.Buf = dAtA
		}
		return protoiface.MarshalOutput{
			NoUnkeyedLiterals: input.NoUnkeyedLiterals,
			Buf:               input.Buf,
		}, nil
	}
	unmarshal := func(input protoiface.UnmarshalInput) (protoiface.UnmarshalOutput, error) {
		x := input.Message.Interface().(*AccessControl)
		if x == nil {
			return protoiface.UnmarshalOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Flags:             input.Flags,
			}, nil
		}
		options := runtime.UnmarshalInputToOptions(input)
		_ = options
		dAtA := input.Buf
		l := len(dAtA)
		iNdEx := 0
		for iNdEx < l {
			preIndex := iNdEx
			var wire uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
				}
				if iNdEx >= l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				wire |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			fieldNum := int32(wire >> 3)
			wireType := int(wire & 0x7)
			if wireType == 4 {
				return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: AccessControl: wiretype end group for non-group")
			}
			if fieldNum <= 0 {
				return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: AccessControl: illegal tag %d (wire type %d)", fieldNum, wire)
			}
			switch fieldNum {
			case 1:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Create", wireType)
				}
				var msglen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					msglen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if msglen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + msglen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				if x.Create == nil {
					x.Create = &AccessControlType{}
				}
				if err := options.Unmarshal(dAtA[iNdEx:postIndex], x.Create); err != nil {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, err
				}
				iNdEx = postIndex
			case 2:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Call", wireType)
				}
				var msglen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					msglen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if msglen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + msglen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				if x.Call == nil {
					x.Call = &AccessControlType{}
				}
				if err := options.Unmarshal(dAtA[iNdEx:postIndex], x.Call); err != nil {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, err
				}
				iNdEx = postIndex
			default:
				iNdEx = preIndex
				skippy, err := runtime.Skip(dAtA[iNdEx:])
				if err != nil {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, err
				}
				if (skippy < 0) || (iNdEx+skippy) < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if (iNdEx + skippy) > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				if !options.DiscardUnknown {
					x.unknownFields = append(x.unknownFields, dAtA[iNdEx:iNdEx+skippy]...)
				}
				iNdEx += skippy
			}
		}

		if iNdEx > l {
			return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
		}
		return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, nil
	}
	return &protoiface.Methods{
		NoUnkeyedLiterals: struct{}{},
		Flags:             protoiface.SupportMarshalDeterministic | protoiface.SupportUnmarshalDiscardUnknown,
		Size:              size,
		Marshal:           marshal,
		Unmarshal:         unmarshal,
		Merge:             nil,
		CheckInitialized:  nil,
	}
}

var _ protoreflect.List = (*_AccessControlType_2_list)(nil)

type _AccessControlType_2_list struct {
	list *[]string
}

func (x *_AccessControlType_2_list) Len() int {
	if x.list == nil {
		return 0
	}
	return len(*x.list)
}

func (x *_AccessControlType_2_list) Get(i int) protoreflect.Value {
	return protoreflect.ValueOfString((*x.list)[i])
}

func (x *_AccessControlType_2_list) Set(i int, value protoreflect.Value) {
	valueUnwrapped := value.String()
	concreteValue := valueUnwrapped
	(*x.list)[i] = concreteValue
}

func (x *_AccessControlType_2_list) Append(value protoreflect.Value) {
	valueUnwrapped := value.String()
	concreteValue := valueUnwrapped
	*x.list = append(*x.list, concreteValue)
}

func (x *_AccessControlType_2_list) AppendMutable() protoreflect.Value {
	panic(fmt.Errorf("AppendMutable can not be called on message AccessControlType at list field AccessControlList as it is not of Message kind"))
}

func (x *_AccessControlType_2_list) Truncate(n int) {
	*x.list = (*x.list)[:n]
}

func (x *_AccessControlType_2_list) NewElement() protoreflect.Value {
	v := ""
	return protoreflect.ValueOfString(v)
}

func (x *_AccessControlType_2_list) IsValid() bool {
	return x.list != nil
}

var (
	md_AccessControlType                     protoreflect.MessageDescriptor
	fd_AccessControlType_access_type         protoreflect.FieldDescriptor
	fd_AccessControlType_access_control_list protoreflect.FieldDescriptor
)

func init() {
	file_cosmos_evm_vm_v1_evm_proto_init()
	md_AccessControlType = File_cosmos_evm_vm_v1_evm_proto.Messages().ByName("AccessControlType")
	fd_AccessControlType_access_type = md_AccessControlType.Fields().ByName("access_type")
	fd_AccessControlType_access_control_list = md_AccessControlType.Fields().ByName("access_control_list")
}

var _ protoreflect.Message = (*fastReflection_AccessControlType)(nil)

type fastReflection_AccessControlType AccessControlType

func (x *AccessControlType) ProtoReflect() protoreflect.Message {
	return (*fastReflection_AccessControlType)(x)
}

func (x *AccessControlType) slowProtoReflect() protoreflect.Message {
	mi := &file_cosmos_evm_vm_v1_evm_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

var _fastReflection_AccessControlType_messageType fastReflection_AccessControlType_messageType
var _ protoreflect.MessageType = fastReflection_AccessControlType_messageType{}

type fastReflection_AccessControlType_messageType struct{}

func (x fastReflection_AccessControlType_messageType) Zero() protoreflect.Message {
	return (*fastReflection_AccessControlType)(nil)
}
func (x fastReflection_AccessControlType_messageType) New() protoreflect.Message {
	return new(fastReflection_AccessControlType)
}
func (x fastReflection_AccessControlType_messageType) Descriptor() protoreflect.MessageDescriptor {
	return md_AccessControlType
}

// Descriptor returns message descriptor, which contains only the protobuf
// type information for the message.
func (x *fastReflection_AccessControlType) Descriptor() protoreflect.MessageDescriptor {
	return md_AccessControlType
}

// Type returns the message type, which encapsulates both Go and protobuf
// type information. If the Go type information is not needed,
// it is recommended that the message descriptor be used instead.
func (x *fastReflection_AccessControlType) Type() protoreflect.MessageType {
	return _fastReflection_AccessControlType_messageType
}

// New returns a newly allocated and mutable empty message.
func (x *fastReflection_AccessControlType) New() protoreflect.Message {
	return new(fastReflection_AccessControlType)
}

// Interface unwraps the message reflection interface and
// returns the underlying ProtoMessage interface.
func (x *fastReflection_AccessControlType) Interface() protoreflect.ProtoMessage {
	return (*AccessControlType)(x)
}

// Range iterates over every populated field in an undefined order,
// calling f for each field descriptor and value encountered.
// Range returns immediately if f returns false.
// While iterating, mutating operations may only be performed
// on the current field descriptor.
func (x *fastReflection_AccessControlType) Range(f func(protoreflect.FieldDescriptor, protoreflect.Value) bool) {
	if x.AccessType != 0 {
		value := protoreflect.ValueOfEnum((protoreflect.EnumNumber)(x.AccessType))
		if !f(fd_AccessControlType_access_type, value) {
			return
		}
	}
	if len(x.AccessControlList) != 0 {
		value := protoreflect.ValueOfList(&_AccessControlType_2_list{list: &x.AccessControlList})
		if !f(fd_AccessControlType_access_control_list, value) {
			return
		}
	}
}

// Has reports whether a field is populated.
//
// Some fields have the property of nullability where it is possible to
// distinguish between the default value of a field and whether the field
// was explicitly populated with the default value. Singular message fields,
// member fields of a oneof, and proto2 scalar fields are nullable. Such
// fields are populated only if explicitly set.
//
// In other cases (aside from the nullable cases above),
// a proto3 scalar field is populated if it contains a non-zero value, and
// a repeated field is populated if it is non-empty.
func (x *fastReflection_AccessControlType) Has(fd protoreflect.FieldDescriptor) bool {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.AccessControlType.access_type":
		return x.AccessType != 0
	case "cosmos.evm.vm.v1.AccessControlType.access_control_list":
		return len(x.AccessControlList) != 0
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.AccessControlType"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.AccessControlType does not contain field %s", fd.FullName()))
	}
}

// Clear clears the field such that a subsequent Has call reports false.
//
// Clearing an extension field clears both the extension type and value
// associated with the given field number.
//
// Clear is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_AccessControlType) Clear(fd protoreflect.FieldDescriptor) {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.AccessControlType.access_type":
		x.AccessType = 0
	case "cosmos.evm.vm.v1.AccessControlType.access_control_list":
		x.AccessControlList = nil
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.AccessControlType"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.AccessControlType does not contain field %s", fd.FullName()))
	}
}

// Get retrieves the value for a field.
//
// For unpopulated scalars, it returns the default value, where
// the default value of a bytes scalar is guaranteed to be a copy.
// For unpopulated composite types, it returns an empty, read-only view
// of the value; to obtain a mutable reference, use Mutable.
func (x *fastReflection_AccessControlType) Get(descriptor protoreflect.FieldDescriptor) protoreflect.Value {
	switch descriptor.FullName() {
	case "cosmos.evm.vm.v1.AccessControlType.access_type":
		value := x.AccessType
		return protoreflect.ValueOfEnum((protoreflect.EnumNumber)(value))
	case "cosmos.evm.vm.v1.AccessControlType.access_control_list":
		if len(x.AccessControlList) == 0 {
			return protoreflect.ValueOfList(&_AccessControlType_2_list{})
		}
		listValue := &_AccessControlType_2_list{list: &x.AccessControlList}
		return protoreflect.ValueOfList(listValue)
	default:
		if descriptor.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.AccessControlType"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.AccessControlType does not contain field %s", descriptor.FullName()))
	}
}

// Set stores the value for a field.
//
// For a field belonging to a oneof, it implicitly clears any other field
// that may be currently set within the same oneof.
// For extension fields, it implicitly stores the provided ExtensionType.
// When setting a composite type, it is unspecified whether the stored value
// aliases the source's memory in any way. If the composite value is an
// empty, read-only value, then it panics.
//
// Set is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_AccessControlType) Set(fd protoreflect.FieldDescriptor, value protoreflect.Value) {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.AccessControlType.access_type":
		x.AccessType = (AccessType)(value.Enum())
	case "cosmos.evm.vm.v1.AccessControlType.access_control_list":
		lv := value.List()
		clv := lv.(*_AccessControlType_2_list)
		x.AccessControlList = *clv.list
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.AccessControlType"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.AccessControlType does not contain field %s", fd.FullName()))
	}
}

// Mutable returns a mutable reference to a composite type.
//
// If the field is unpopulated, it may allocate a composite value.
// For a field belonging to a oneof, it implicitly clears any other field
// that may be currently set within the same oneof.
// For extension fields, it implicitly stores the provided ExtensionType
// if not already stored.
// It panics if the field does not contain a composite type.
//
// Mutable is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_AccessControlType) Mutable(fd protoreflect.FieldDescriptor) protoreflect.Value {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.AccessControlType.access_control_list":
		if x.AccessControlList == nil {
			x.AccessControlList = []string{}
		}
		value := &_AccessControlType_2_list{list: &x.AccessControlList}
		return protoreflect.ValueOfList(value)
	case "cosmos.evm.vm.v1.AccessControlType.access_type":
		panic(fmt.Errorf("field access_type of message cosmos.evm.vm.v1.AccessControlType is not mutable"))
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.AccessControlType"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.AccessControlType does not contain field %s", fd.FullName()))
	}
}

// NewField returns a new value that is assignable to the field
// for the given descriptor. For scalars, this returns the default value.
// For lists, maps, and messages, this returns a new, empty, mutable value.
func (x *fastReflection_AccessControlType) NewField(fd protoreflect.FieldDescriptor) protoreflect.Value {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.AccessControlType.access_type":
		return protoreflect.ValueOfEnum(0)
	case "cosmos.evm.vm.v1.AccessControlType.access_control_list":
		list := []string{}
		return protoreflect.ValueOfList(&_AccessControlType_2_list{list: &list})
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.AccessControlType"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.AccessControlType does not contain field %s", fd.FullName()))
	}
}

// WhichOneof reports which field within the oneof is populated,
// returning nil if none are populated.
// It panics if the oneof descriptor does not belong to this message.
func (x *fastReflection_AccessControlType) WhichOneof(d protoreflect.OneofDescriptor) protoreflect.FieldDescriptor {
	switch d.FullName() {
	default:
		panic(fmt.Errorf("%s is not a oneof field in cosmos.evm.vm.v1.AccessControlType", d.FullName()))
	}
	panic("unreachable")
}

// GetUnknown retrieves the entire list of unknown fields.
// The caller may only mutate the contents of the RawFields
// if the mutated bytes are stored back into the message with SetUnknown.
func (x *fastReflection_AccessControlType) GetUnknown() protoreflect.RawFields {
	return x.unknownFields
}

// SetUnknown stores an entire list of unknown fields.
// The raw fields must be syntactically valid according to the wire format.
// An implementation may panic if this is not the case.
// Once stored, the caller must not mutate the content of the RawFields.
// An empty RawFields may be passed to clear the fields.
//
// SetUnknown is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_AccessControlType) SetUnknown(fields protoreflect.RawFields) {
	x.unknownFields = fields
}

// IsValid reports whether the message is valid.
//
// An invalid message is an empty, read-only value.
//
// An invalid message often corresponds to a nil pointer of the concrete
// message type, but the details are implementation dependent.
// Validity is not part of the protobuf data model, and may not
// be preserved in marshaling or other operations.
func (x *fastReflection_AccessControlType) IsValid() bool {
	return x != nil
}

// ProtoMethods returns optional fastReflectionFeature-path implementations of various operations.
// This method may return nil.
//
// The returned methods type is identical to
// "google.golang.org/protobuf/runtime/protoiface".Methods.
// Consult the protoiface package documentation for details.
func (x *fastReflection_AccessControlType) ProtoMethods() *protoiface.Methods {
	size := func(input protoiface.SizeInput) protoiface.SizeOutput {
		x := input.Message.Interface().(*AccessControlType)
		if x == nil {
			return protoiface.SizeOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Size:              0,
			}
		}
		options := runtime.SizeInputToOptions(input)
		_ = options
		var n int
		var l int
		_ = l
		if x.AccessType != 0 {
			n += 1 + runtime.Sov(uint64(x.AccessType))
		}
		if len(x.AccessControlList) > 0 {
			for _, s := range x.AccessControlList {
				l = len(s)
				n += 1 + l + runtime.Sov(uint64(l))
			}
		}
		if x.unknownFields != nil {
			n += len(x.unknownFields)
		}
		return protoiface.SizeOutput{
			NoUnkeyedLiterals: input.NoUnkeyedLiterals,
			Size:              n,
		}
	}

	marshal := func(input protoiface.MarshalInput) (protoiface.MarshalOutput, error) {
		x := input.Message.Interface().(*AccessControlType)
		if x == nil {
			return protoiface.MarshalOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Buf:               input.Buf,
			}, nil
		}
		options := runtime.MarshalInputToOptions(input)
		_ = options
		size := options.Size(x)
		dAtA := make([]byte, size)
		i := len(dAtA)
		_ = i
		var l int
		_ = l
		if x.unknownFields != nil {
			i -= len(x.unknownFields)
			copy(dAtA[i:], x.unknownFields)
		}
		if len(x.AccessControlList) > 0 {
			for iNdEx := len(x.AccessControlList) - 1; iNdEx >= 0; iNdEx-- {
				i -= len(x.AccessControlList[iNdEx])
				copy(dAtA[i:], x.AccessControlList[iNdEx])
				i = runtime.EncodeVarint(dAtA, i, uint64(len(x.AccessControlList[iNdEx])))
				i--
				dAtA[i] = 0x12
			}
		}
		if x.AccessType != 0 {
			i = runtime.EncodeVarint(dAtA, i, uint64(x.AccessType))
			i--
			dAtA[i] = 0x8
		}
		if input.Buf != nil {
			input.Buf = append(input.Buf, dAtA...)
		} else {
			input.Buf = dAtA
		}
		return protoiface.MarshalOutput{
			NoUnkeyedLiterals: input.NoUnkeyedLiterals,
			Buf:               input.Buf,
		}, nil
	}
	unmarshal := func(input protoiface.UnmarshalInput) (protoiface.UnmarshalOutput, error) {
		x := input.Message.Interface().(*AccessControlType)
		if x == nil {
			return protoiface.UnmarshalOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Flags:             input.Flags,
			}, nil
		}
		options := runtime.UnmarshalInputToOptions(input)
		_ = options
		dAtA := input.Buf
		l := len(dAtA)
		iNdEx := 0
		for iNdEx < l {
			preIndex := iNdEx
			var wire uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
				}
				if iNdEx >= l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				wire |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			fieldNum := int32(wire >> 3)
			wireType := int(wire & 0x7)
			if wireType == 4 {
				return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: AccessControlType: wiretype end group for non-group")
			}
			if fieldNum <= 0 {
				return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: AccessControlType: illegal tag %d (wire type %d)", fieldNum, wire)
			}
			switch fieldNum {
			case 1:
				if wireType != 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field AccessType", wireType)
				}
				x.AccessType = 0
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					x.AccessType |= AccessType(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
			case 2:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field AccessControlList", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.AccessControlList = append(x.AccessControlList, string(dAtA[iNdEx:postIndex]))
				iNdEx = postIndex
			default:
				iNdEx = preIndex
				skippy, err := runtime.Skip(dAtA[iNdEx:])
				if err != nil {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, err
				}
				if (skippy < 0) || (iNdEx+skippy) < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if (iNdEx + skippy) > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				if !options.DiscardUnknown {
					x.unknownFields = append(x.unknownFields, dAtA[iNdEx:iNdEx+skippy]...)
				}
				iNdEx += skippy
			}
		}

		if iNdEx > l {
			return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
		}
		return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, nil
	}
	return &protoiface.Methods{
		NoUnkeyedLiterals: struct{}{},
		Flags:             protoiface.SupportMarshalDeterministic | protoiface.SupportUnmarshalDiscardUnknown,
		Size:              size,
		Marshal:           marshal,
		Unmarshal:         unmarshal,
		Merge:             nil,
		CheckInitialized:  nil,
	}
}

var (
	md_ChainConfig                      protoreflect.MessageDescriptor
	fd_ChainConfig_homestead_block      protoreflect.FieldDescriptor
	fd_ChainConfig_dao_fork_block       protoreflect.FieldDescriptor
	fd_ChainConfig_dao_fork_support     protoreflect.FieldDescriptor
	fd_ChainConfig_eip150_block         protoreflect.FieldDescriptor
	fd_ChainConfig_eip155_block         protoreflect.FieldDescriptor
	fd_ChainConfig_eip158_block         protoreflect.FieldDescriptor
	fd_ChainConfig_byzantium_block      protoreflect.FieldDescriptor
	fd_ChainConfig_constantinople_block protoreflect.FieldDescriptor
	fd_ChainConfig_petersburg_block     protoreflect.FieldDescriptor
	fd_ChainConfig_istanbul_block       protoreflect.FieldDescriptor
	fd_ChainConfig_muir_glacier_block   protoreflect.FieldDescriptor
	fd_ChainConfig_berlin_block         protoreflect.FieldDescriptor
	fd_ChainConfig_london_block         protoreflect.FieldDescriptor
	fd_ChainConfig_arrow_glacier_block  protoreflect.FieldDescriptor
	fd_ChainConfig_gray_glacier_block   protoreflect.FieldDescriptor
	fd_ChainConfig_merge_netsplit_block protoreflect.FieldDescriptor
	fd_ChainConfig_chain_id             protoreflect.FieldDescriptor
	fd_ChainConfig_denom                protoreflect.FieldDescriptor
	fd_ChainConfig_decimals             protoreflect.FieldDescriptor
	fd_ChainConfig_shanghai_time        protoreflect.FieldDescriptor
	fd_ChainConfig_cancun_time          protoreflect.FieldDescriptor
	fd_ChainConfig_prague_time          protoreflect.FieldDescriptor
	fd_ChainConfig_verkle_time          protoreflect.FieldDescriptor
	fd_ChainConfig_osaka_time           protoreflect.FieldDescriptor
)

func init() {
	file_cosmos_evm_vm_v1_evm_proto_init()
	md_ChainConfig = File_cosmos_evm_vm_v1_evm_proto.Messages().ByName("ChainConfig")
	fd_ChainConfig_homestead_block = md_ChainConfig.Fields().ByName("homestead_block")
	fd_ChainConfig_dao_fork_block = md_ChainConfig.Fields().ByName("dao_fork_block")
	fd_ChainConfig_dao_fork_support = md_ChainConfig.Fields().ByName("dao_fork_support")
	fd_ChainConfig_eip150_block = md_ChainConfig.Fields().ByName("eip150_block")
	fd_ChainConfig_eip155_block = md_ChainConfig.Fields().ByName("eip155_block")
	fd_ChainConfig_eip158_block = md_ChainConfig.Fields().ByName("eip158_block")
	fd_ChainConfig_byzantium_block = md_ChainConfig.Fields().ByName("byzantium_block")
	fd_ChainConfig_constantinople_block = md_ChainConfig.Fields().ByName("constantinople_block")
	fd_ChainConfig_petersburg_block = md_ChainConfig.Fields().ByName("petersburg_block")
	fd_ChainConfig_istanbul_block = md_ChainConfig.Fields().ByName("istanbul_block")
	fd_ChainConfig_muir_glacier_block = md_ChainConfig.Fields().ByName("muir_glacier_block")
	fd_ChainConfig_berlin_block = md_ChainConfig.Fields().ByName("berlin_block")
	fd_ChainConfig_london_block = md_ChainConfig.Fields().ByName("london_block")
	fd_ChainConfig_arrow_glacier_block = md_ChainConfig.Fields().ByName("arrow_glacier_block")
	fd_ChainConfig_gray_glacier_block = md_ChainConfig.Fields().ByName("gray_glacier_block")
	fd_ChainConfig_merge_netsplit_block = md_ChainConfig.Fields().ByName("merge_netsplit_block")
	fd_ChainConfig_chain_id = md_ChainConfig.Fields().ByName("chain_id")
	fd_ChainConfig_denom = md_ChainConfig.Fields().ByName("denom")
	fd_ChainConfig_decimals = md_ChainConfig.Fields().ByName("decimals")
	fd_ChainConfig_shanghai_time = md_ChainConfig.Fields().ByName("shanghai_time")
	fd_ChainConfig_cancun_time = md_ChainConfig.Fields().ByName("cancun_time")
	fd_ChainConfig_prague_time = md_ChainConfig.Fields().ByName("prague_time")
	fd_ChainConfig_verkle_time = md_ChainConfig.Fields().ByName("verkle_time")
	fd_ChainConfig_osaka_time = md_ChainConfig.Fields().ByName("osaka_time")
}

var _ protoreflect.Message = (*fastReflection_ChainConfig)(nil)

type fastReflection_ChainConfig ChainConfig

func (x *ChainConfig) ProtoReflect() protoreflect.Message {
	return (*fastReflection_ChainConfig)(x)
}

func (x *ChainConfig) slowProtoReflect() protoreflect.Message {
	mi := &file_cosmos_evm_vm_v1_evm_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

var _fastReflection_ChainConfig_messageType fastReflection_ChainConfig_messageType
var _ protoreflect.MessageType = fastReflection_ChainConfig_messageType{}

type fastReflection_ChainConfig_messageType struct{}

func (x fastReflection_ChainConfig_messageType) Zero() protoreflect.Message {
	return (*fastReflection_ChainConfig)(nil)
}
func (x fastReflection_ChainConfig_messageType) New() protoreflect.Message {
	return new(fastReflection_ChainConfig)
}
func (x fastReflection_ChainConfig_messageType) Descriptor() protoreflect.MessageDescriptor {
	return md_ChainConfig
}

// Descriptor returns message descriptor, which contains only the protobuf
// type information for the message.
func (x *fastReflection_ChainConfig) Descriptor() protoreflect.MessageDescriptor {
	return md_ChainConfig
}

// Type returns the message type, which encapsulates both Go and protobuf
// type information. If the Go type information is not needed,
// it is recommended that the message descriptor be used instead.
func (x *fastReflection_ChainConfig) Type() protoreflect.MessageType {
	return _fastReflection_ChainConfig_messageType
}

// New returns a newly allocated and mutable empty message.
func (x *fastReflection_ChainConfig) New() protoreflect.Message {
	return new(fastReflection_ChainConfig)
}

// Interface unwraps the message reflection interface and
// returns the underlying ProtoMessage interface.
func (x *fastReflection_ChainConfig) Interface() protoreflect.ProtoMessage {
	return (*ChainConfig)(x)
}

// Range iterates over every populated field in an undefined order,
// calling f for each field descriptor and value encountered.
// Range returns immediately if f returns false.
// While iterating, mutating operations may only be performed
// on the current field descriptor.
func (x *fastReflection_ChainConfig) Range(f func(protoreflect.FieldDescriptor, protoreflect.Value) bool) {
	if x.HomesteadBlock != "" {
		value := protoreflect.ValueOfString(x.HomesteadBlock)
		if !f(fd_ChainConfig_homestead_block, value) {
			return
		}
	}
	if x.DaoForkBlock != "" {
		value := protoreflect.ValueOfString(x.DaoForkBlock)
		if !f(fd_ChainConfig_dao_fork_block, value) {
			return
		}
	}
	if x.DaoForkSupport != false {
		value := protoreflect.ValueOfBool(x.DaoForkSupport)
		if !f(fd_ChainConfig_dao_fork_support, value) {
			return
		}
	}
	if x.Eip150Block != "" {
		value := protoreflect.ValueOfString(x.Eip150Block)
		if !f(fd_ChainConfig_eip150_block, value) {
			return
		}
	}
	if x.Eip155Block != "" {
		value := protoreflect.ValueOfString(x.Eip155Block)
		if !f(fd_ChainConfig_eip155_block, value) {
			return
		}
	}
	if x.Eip158Block != "" {
		value := protoreflect.ValueOfString(x.Eip158Block)
		if !f(fd_ChainConfig_eip158_block, value) {
			return
		}
	}
	if x.ByzantiumBlock != "" {
		value := protoreflect.ValueOfString(x.ByzantiumBlock)
		if !f(fd_ChainConfig_byzantium_block, value) {
			return
		}
	}
	if x.ConstantinopleBlock != "" {
		value := protoreflect.ValueOfString(x.ConstantinopleBlock)
		if !f(fd_ChainConfig_constantinople_block, value) {
			return
		}
	}
	if x.PetersburgBlock != "" {
		value := protoreflect.ValueOfString(x.PetersburgBlock)
		if !f(fd_ChainConfig_petersburg_block, value) {
			return
		}
	}
	if x.IstanbulBlock != "" {
		value := protoreflect.ValueOfString(x.IstanbulBlock)
		if !f(fd_ChainConfig_istanbul_block, value) {
			return
		}
	}
	if x.MuirGlacierBlock != "" {
		value := protoreflect.ValueOfString(x.MuirGlacierBlock)
		if !f(fd_ChainConfig_muir_glacier_block, value) {
			return
		}
	}
	if x.BerlinBlock != "" {
		value := protoreflect.ValueOfString(x.BerlinBlock)
		if !f(fd_ChainConfig_berlin_block, value) {
			return
		}
	}
	if x.LondonBlock != "" {
		value := protoreflect.ValueOfString(x.LondonBlock)
		if !f(fd_ChainConfig_london_block, value) {
			return
		}
	}
	if x.ArrowGlacierBlock != "" {
		value := protoreflect.ValueOfString(x.ArrowGlacierBlock)
		if !f(fd_ChainConfig_arrow_glacier_block, value) {
			return
		}
	}
	if x.GrayGlacierBlock != "" {
		value := protoreflect.ValueOfString(x.GrayGlacierBlock)
		if !f(fd_ChainConfig_gray_glacier_block, value) {
			return
		}
	}
	if x.MergeNetsplitBlock != "" {
		value := protoreflect.ValueOfString(x.MergeNetsplitBlock)
		if !f(fd_ChainConfig_merge_netsplit_block, value) {
			return
		}
	}
	if x.ChainId != uint64(0) {
		value := protoreflect.ValueOfUint64(x.ChainId)
		if !f(fd_ChainConfig_chain_id, value) {
			return
		}
	}
	if x.Denom != "" {
		value := protoreflect.ValueOfString(x.Denom)
		if !f(fd_ChainConfig_denom, value) {
			return
		}
	}
	if x.Decimals != uint64(0) {
		value := protoreflect.ValueOfUint64(x.Decimals)
		if !f(fd_ChainConfig_decimals, value) {
			return
		}
	}
	if x.ShanghaiTime != "" {
		value := protoreflect.ValueOfString(x.ShanghaiTime)
		if !f(fd_ChainConfig_shanghai_time, value) {
			return
		}
	}
	if x.CancunTime != "" {
		value := protoreflect.ValueOfString(x.CancunTime)
		if !f(fd_ChainConfig_cancun_time, value) {
			return
		}
	}
	if x.PragueTime != "" {
		value := protoreflect.ValueOfString(x.PragueTime)
		if !f(fd_ChainConfig_prague_time, value) {
			return
		}
	}
	if x.VerkleTime != "" {
		value := protoreflect.ValueOfString(x.VerkleTime)
		if !f(fd_ChainConfig_verkle_time, value) {
			return
		}
	}
	if x.OsakaTime != "" {
		value := protoreflect.ValueOfString(x.OsakaTime)
		if !f(fd_ChainConfig_osaka_time, value) {
			return
		}
	}
}

// Has reports whether a field is populated.
//
// Some fields have the property of nullability where it is possible to
// distinguish between the default value of a field and whether the field
// was explicitly populated with the default value. Singular message fields,
// member fields of a oneof, and proto2 scalar fields are nullable. Such
// fields are populated only if explicitly set.
//
// In other cases (aside from the nullable cases above),
// a proto3 scalar field is populated if it contains a non-zero value, and
// a repeated field is populated if it is non-empty.
func (x *fastReflection_ChainConfig) Has(fd protoreflect.FieldDescriptor) bool {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.ChainConfig.homestead_block":
		return x.HomesteadBlock != ""
	case "cosmos.evm.vm.v1.ChainConfig.dao_fork_block":
		return x.DaoForkBlock != ""
	case "cosmos.evm.vm.v1.ChainConfig.dao_fork_support":
		return x.DaoForkSupport != false
	case "cosmos.evm.vm.v1.ChainConfig.eip150_block":
		return x.Eip150Block != ""
	case "cosmos.evm.vm.v1.ChainConfig.eip155_block":
		return x.Eip155Block != ""
	case "cosmos.evm.vm.v1.ChainConfig.eip158_block":
		return x.Eip158Block != ""
	case "cosmos.evm.vm.v1.ChainConfig.byzantium_block":
		return x.ByzantiumBlock != ""
	case "cosmos.evm.vm.v1.ChainConfig.constantinople_block":
		return x.ConstantinopleBlock != ""
	case "cosmos.evm.vm.v1.ChainConfig.petersburg_block":
		return x.PetersburgBlock != ""
	case "cosmos.evm.vm.v1.ChainConfig.istanbul_block":
		return x.IstanbulBlock != ""
	case "cosmos.evm.vm.v1.ChainConfig.muir_glacier_block":
		return x.MuirGlacierBlock != ""
	case "cosmos.evm.vm.v1.ChainConfig.berlin_block":
		return x.BerlinBlock != ""
	case "cosmos.evm.vm.v1.ChainConfig.london_block":
		return x.LondonBlock != ""
	case "cosmos.evm.vm.v1.ChainConfig.arrow_glacier_block":
		return x.ArrowGlacierBlock != ""
	case "cosmos.evm.vm.v1.ChainConfig.gray_glacier_block":
		return x.GrayGlacierBlock != ""
	case "cosmos.evm.vm.v1.ChainConfig.merge_netsplit_block":
		return x.MergeNetsplitBlock != ""
	case "cosmos.evm.vm.v1.ChainConfig.chain_id":
		return x.ChainId != uint64(0)
	case "cosmos.evm.vm.v1.ChainConfig.denom":
		return x.Denom != ""
	case "cosmos.evm.vm.v1.ChainConfig.decimals":
		return x.Decimals != uint64(0)
	case "cosmos.evm.vm.v1.ChainConfig.shanghai_time":
		return x.ShanghaiTime != ""
	case "cosmos.evm.vm.v1.ChainConfig.cancun_time":
		return x.CancunTime != ""
	case "cosmos.evm.vm.v1.ChainConfig.prague_time":
		return x.PragueTime != ""
	case "cosmos.evm.vm.v1.ChainConfig.verkle_time":
		return x.VerkleTime != ""
	case "cosmos.evm.vm.v1.ChainConfig.osaka_time":
		return x.OsakaTime != ""
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.ChainConfig"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.ChainConfig does not contain field %s", fd.FullName()))
	}
}

// Clear clears the field such that a subsequent Has call reports false.
//
// Clearing an extension field clears both the extension type and value
// associated with the given field number.
//
// Clear is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_ChainConfig) Clear(fd protoreflect.FieldDescriptor) {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.ChainConfig.homestead_block":
		x.HomesteadBlock = ""
	case "cosmos.evm.vm.v1.ChainConfig.dao_fork_block":
		x.DaoForkBlock = ""
	case "cosmos.evm.vm.v1.ChainConfig.dao_fork_support":
		x.DaoForkSupport = false
	case "cosmos.evm.vm.v1.ChainConfig.eip150_block":
		x.Eip150Block = ""
	case "cosmos.evm.vm.v1.ChainConfig.eip155_block":
		x.Eip155Block = ""
	case "cosmos.evm.vm.v1.ChainConfig.eip158_block":
		x.Eip158Block = ""
	case "cosmos.evm.vm.v1.ChainConfig.byzantium_block":
		x.ByzantiumBlock = ""
	case "cosmos.evm.vm.v1.ChainConfig.constantinople_block":
		x.ConstantinopleBlock = ""
	case "cosmos.evm.vm.v1.ChainConfig.petersburg_block":
		x.PetersburgBlock = ""
	case "cosmos.evm.vm.v1.ChainConfig.istanbul_block":
		x.IstanbulBlock = ""
	case "cosmos.evm.vm.v1.ChainConfig.muir_glacier_block":
		x.MuirGlacierBlock = ""
	case "cosmos.evm.vm.v1.ChainConfig.berlin_block":
		x.BerlinBlock = ""
	case "cosmos.evm.vm.v1.ChainConfig.london_block":
		x.LondonBlock = ""
	case "cosmos.evm.vm.v1.ChainConfig.arrow_glacier_block":
		x.ArrowGlacierBlock = ""
	case "cosmos.evm.vm.v1.ChainConfig.gray_glacier_block":
		x.GrayGlacierBlock = ""
	case "cosmos.evm.vm.v1.ChainConfig.merge_netsplit_block":
		x.MergeNetsplitBlock = ""
	case "cosmos.evm.vm.v1.ChainConfig.chain_id":
		x.ChainId = uint64(0)
	case "cosmos.evm.vm.v1.ChainConfig.denom":
		x.Denom = ""
	case "cosmos.evm.vm.v1.ChainConfig.decimals":
		x.Decimals = uint64(0)
	case "cosmos.evm.vm.v1.ChainConfig.shanghai_time":
		x.ShanghaiTime = ""
	case "cosmos.evm.vm.v1.ChainConfig.cancun_time":
		x.CancunTime = ""
	case "cosmos.evm.vm.v1.ChainConfig.prague_time":
		x.PragueTime = ""
	case "cosmos.evm.vm.v1.ChainConfig.verkle_time":
		x.VerkleTime = ""
	case "cosmos.evm.vm.v1.ChainConfig.osaka_time":
		x.OsakaTime = ""
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.ChainConfig"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.ChainConfig does not contain field %s", fd.FullName()))
	}
}

// Get retrieves the value for a field.
//
// For unpopulated scalars, it returns the default value, where
// the default value of a bytes scalar is guaranteed to be a copy.
// For unpopulated composite types, it returns an empty, read-only view
// of the value; to obtain a mutable reference, use Mutable.
func (x *fastReflection_ChainConfig) Get(descriptor protoreflect.FieldDescriptor) protoreflect.Value {
	switch descriptor.FullName() {
	case "cosmos.evm.vm.v1.ChainConfig.homestead_block":
		value := x.HomesteadBlock
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.ChainConfig.dao_fork_block":
		value := x.DaoForkBlock
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.ChainConfig.dao_fork_support":
		value := x.DaoForkSupport
		return protoreflect.ValueOfBool(value)
	case "cosmos.evm.vm.v1.ChainConfig.eip150_block":
		value := x.Eip150Block
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.ChainConfig.eip155_block":
		value := x.Eip155Block
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.ChainConfig.eip158_block":
		value := x.Eip158Block
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.ChainConfig.byzantium_block":
		value := x.ByzantiumBlock
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.ChainConfig.constantinople_block":
		value := x.ConstantinopleBlock
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.ChainConfig.petersburg_block":
		value := x.PetersburgBlock
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.ChainConfig.istanbul_block":
		value := x.IstanbulBlock
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.ChainConfig.muir_glacier_block":
		value := x.MuirGlacierBlock
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.ChainConfig.berlin_block":
		value := x.BerlinBlock
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.ChainConfig.london_block":
		value := x.LondonBlock
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.ChainConfig.arrow_glacier_block":
		value := x.ArrowGlacierBlock
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.ChainConfig.gray_glacier_block":
		value := x.GrayGlacierBlock
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.ChainConfig.merge_netsplit_block":
		value := x.MergeNetsplitBlock
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.ChainConfig.chain_id":
		value := x.ChainId
		return protoreflect.ValueOfUint64(value)
	case "cosmos.evm.vm.v1.ChainConfig.denom":
		value := x.Denom
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.ChainConfig.decimals":
		value := x.Decimals
		return protoreflect.ValueOfUint64(value)
	case "cosmos.evm.vm.v1.ChainConfig.shanghai_time":
		value := x.ShanghaiTime
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.ChainConfig.cancun_time":
		value := x.CancunTime
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.ChainConfig.prague_time":
		value := x.PragueTime
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.ChainConfig.verkle_time":
		value := x.VerkleTime
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.ChainConfig.osaka_time":
		value := x.OsakaTime
		return protoreflect.ValueOfString(value)
	default:
		if descriptor.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.ChainConfig"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.ChainConfig does not contain field %s", descriptor.FullName()))
	}
}

// Set stores the value for a field.
//
// For a field belonging to a oneof, it implicitly clears any other field
// that may be currently set within the same oneof.
// For extension fields, it implicitly stores the provided ExtensionType.
// When setting a composite type, it is unspecified whether the stored value
// aliases the source's memory in any way. If the composite value is an
// empty, read-only value, then it panics.
//
// Set is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_ChainConfig) Set(fd protoreflect.FieldDescriptor, value protoreflect.Value) {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.ChainConfig.homestead_block":
		x.HomesteadBlock = value.Interface().(string)
	case "cosmos.evm.vm.v1.ChainConfig.dao_fork_block":
		x.DaoForkBlock = value.Interface().(string)
	case "cosmos.evm.vm.v1.ChainConfig.dao_fork_support":
		x.DaoForkSupport = value.Bool()
	case "cosmos.evm.vm.v1.ChainConfig.eip150_block":
		x.Eip150Block = value.Interface().(string)
	case "cosmos.evm.vm.v1.ChainConfig.eip155_block":
		x.Eip155Block = value.Interface().(string)
	case "cosmos.evm.vm.v1.ChainConfig.eip158_block":
		x.Eip158Block = value.Interface().(string)
	case "cosmos.evm.vm.v1.ChainConfig.byzantium_block":
		x.ByzantiumBlock = value.Interface().(string)
	case "cosmos.evm.vm.v1.ChainConfig.constantinople_block":
		x.ConstantinopleBlock = value.Interface().(string)
	case "cosmos.evm.vm.v1.ChainConfig.petersburg_block":
		x.PetersburgBlock = value.Interface().(string)
	case "cosmos.evm.vm.v1.ChainConfig.istanbul_block":
		x.IstanbulBlock = value.Interface().(string)
	case "cosmos.evm.vm.v1.ChainConfig.muir_glacier_block":
		x.MuirGlacierBlock = value.Interface().(string)
	case "cosmos.evm.vm.v1.ChainConfig.berlin_block":
		x.BerlinBlock = value.Interface().(string)
	case "cosmos.evm.vm.v1.ChainConfig.london_block":
		x.LondonBlock = value.Interface().(string)
	case "cosmos.evm.vm.v1.ChainConfig.arrow_glacier_block":
		x.ArrowGlacierBlock = value.Interface().(string)
	case "cosmos.evm.vm.v1.ChainConfig.gray_glacier_block":
		x.GrayGlacierBlock = value.Interface().(string)
	case "cosmos.evm.vm.v1.ChainConfig.merge_netsplit_block":
		x.MergeNetsplitBlock = value.Interface().(string)
	case "cosmos.evm.vm.v1.ChainConfig.chain_id":
		x.ChainId = value.Uint()
	case "cosmos.evm.vm.v1.ChainConfig.denom":
		x.Denom = value.Interface().(string)
	case "cosmos.evm.vm.v1.ChainConfig.decimals":
		x.Decimals = value.Uint()
	case "cosmos.evm.vm.v1.ChainConfig.shanghai_time":
		x.ShanghaiTime = value.Interface().(string)
	case "cosmos.evm.vm.v1.ChainConfig.cancun_time":
		x.CancunTime = value.Interface().(string)
	case "cosmos.evm.vm.v1.ChainConfig.prague_time":
		x.PragueTime = value.Interface().(string)
	case "cosmos.evm.vm.v1.ChainConfig.verkle_time":
		x.VerkleTime = value.Interface().(string)
	case "cosmos.evm.vm.v1.ChainConfig.osaka_time":
		x.OsakaTime = value.Interface().(string)
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.ChainConfig"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.ChainConfig does not contain field %s", fd.FullName()))
	}
}

// Mutable returns a mutable reference to a composite type.
//
// If the field is unpopulated, it may allocate a composite value.
// For a field belonging to a oneof, it implicitly clears any other field
// that may be currently set within the same oneof.
// For extension fields, it implicitly stores the provided ExtensionType
// if not already stored.
// It panics if the field does not contain a composite type.
//
// Mutable is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_ChainConfig) Mutable(fd protoreflect.FieldDescriptor) protoreflect.Value {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.ChainConfig.homestead_block":
		panic(fmt.Errorf("field homestead_block of message cosmos.evm.vm.v1.ChainConfig is not mutable"))
	case "cosmos.evm.vm.v1.ChainConfig.dao_fork_block":
		panic(fmt.Errorf("field dao_fork_block of message cosmos.evm.vm.v1.ChainConfig is not mutable"))
	case "cosmos.evm.vm.v1.ChainConfig.dao_fork_support":
		panic(fmt.Errorf("field dao_fork_support of message cosmos.evm.vm.v1.ChainConfig is not mutable"))
	case "cosmos.evm.vm.v1.ChainConfig.eip150_block":
		panic(fmt.Errorf("field eip150_block of message cosmos.evm.vm.v1.ChainConfig is not mutable"))
	case "cosmos.evm.vm.v1.ChainConfig.eip155_block":
		panic(fmt.Errorf("field eip155_block of message cosmos.evm.vm.v1.ChainConfig is not mutable"))
	case "cosmos.evm.vm.v1.ChainConfig.eip158_block":
		panic(fmt.Errorf("field eip158_block of message cosmos.evm.vm.v1.ChainConfig is not mutable"))
	case "cosmos.evm.vm.v1.ChainConfig.byzantium_block":
		panic(fmt.Errorf("field byzantium_block of message cosmos.evm.vm.v1.ChainConfig is not mutable"))
	case "cosmos.evm.vm.v1.ChainConfig.constantinople_block":
		panic(fmt.Errorf("field constantinople_block of message cosmos.evm.vm.v1.ChainConfig is not mutable"))
	case "cosmos.evm.vm.v1.ChainConfig.petersburg_block":
		panic(fmt.Errorf("field petersburg_block of message cosmos.evm.vm.v1.ChainConfig is not mutable"))
	case "cosmos.evm.vm.v1.ChainConfig.istanbul_block":
		panic(fmt.Errorf("field istanbul_block of message cosmos.evm.vm.v1.ChainConfig is not mutable"))
	case "cosmos.evm.vm.v1.ChainConfig.muir_glacier_block":
		panic(fmt.Errorf("field muir_glacier_block of message cosmos.evm.vm.v1.ChainConfig is not mutable"))
	case "cosmos.evm.vm.v1.ChainConfig.berlin_block":
		panic(fmt.Errorf("field berlin_block of message cosmos.evm.vm.v1.ChainConfig is not mutable"))
	case "cosmos.evm.vm.v1.ChainConfig.london_block":
		panic(fmt.Errorf("field london_block of message cosmos.evm.vm.v1.ChainConfig is not mutable"))
	case "cosmos.evm.vm.v1.ChainConfig.arrow_glacier_block":
		panic(fmt.Errorf("field arrow_glacier_block of message cosmos.evm.vm.v1.ChainConfig is not mutable"))
	case "cosmos.evm.vm.v1.ChainConfig.gray_glacier_block":
		panic(fmt.Errorf("field gray_glacier_block of message cosmos.evm.vm.v1.ChainConfig is not mutable"))
	case "cosmos.evm.vm.v1.ChainConfig.merge_netsplit_block":
		panic(fmt.Errorf("field merge_netsplit_block of message cosmos.evm.vm.v1.ChainConfig is not mutable"))
	case "cosmos.evm.vm.v1.ChainConfig.chain_id":
		panic(fmt.Errorf("field chain_id of message cosmos.evm.vm.v1.ChainConfig is not mutable"))
	case "cosmos.evm.vm.v1.ChainConfig.denom":
		panic(fmt.Errorf("field denom of message cosmos.evm.vm.v1.ChainConfig is not mutable"))
	case "cosmos.evm.vm.v1.ChainConfig.decimals":
		panic(fmt.Errorf("field decimals of message cosmos.evm.vm.v1.ChainConfig is not mutable"))
	case "cosmos.evm.vm.v1.ChainConfig.shanghai_time":
		panic(fmt.Errorf("field shanghai_time of message cosmos.evm.vm.v1.ChainConfig is not mutable"))
	case "cosmos.evm.vm.v1.ChainConfig.cancun_time":
		panic(fmt.Errorf("field cancun_time of message cosmos.evm.vm.v1.ChainConfig is not mutable"))
	case "cosmos.evm.vm.v1.ChainConfig.prague_time":
		panic(fmt.Errorf("field prague_time of message cosmos.evm.vm.v1.ChainConfig is not mutable"))
	case "cosmos.evm.vm.v1.ChainConfig.verkle_time":
		panic(fmt.Errorf("field verkle_time of message cosmos.evm.vm.v1.ChainConfig is not mutable"))
	case "cosmos.evm.vm.v1.ChainConfig.osaka_time":
		panic(fmt.Errorf("field osaka_time of message cosmos.evm.vm.v1.ChainConfig is not mutable"))
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.ChainConfig"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.ChainConfig does not contain field %s", fd.FullName()))
	}
}

// NewField returns a new value that is assignable to the field
// for the given descriptor. For scalars, this returns the default value.
// For lists, maps, and messages, this returns a new, empty, mutable value.
func (x *fastReflection_ChainConfig) NewField(fd protoreflect.FieldDescriptor) protoreflect.Value {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.ChainConfig.homestead_block":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.ChainConfig.dao_fork_block":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.ChainConfig.dao_fork_support":
		return protoreflect.ValueOfBool(false)
	case "cosmos.evm.vm.v1.ChainConfig.eip150_block":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.ChainConfig.eip155_block":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.ChainConfig.eip158_block":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.ChainConfig.byzantium_block":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.ChainConfig.constantinople_block":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.ChainConfig.petersburg_block":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.ChainConfig.istanbul_block":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.ChainConfig.muir_glacier_block":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.ChainConfig.berlin_block":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.ChainConfig.london_block":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.ChainConfig.arrow_glacier_block":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.ChainConfig.gray_glacier_block":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.ChainConfig.merge_netsplit_block":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.ChainConfig.chain_id":
		return protoreflect.ValueOfUint64(uint64(0))
	case "cosmos.evm.vm.v1.ChainConfig.denom":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.ChainConfig.decimals":
		return protoreflect.ValueOfUint64(uint64(0))
	case "cosmos.evm.vm.v1.ChainConfig.shanghai_time":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.ChainConfig.cancun_time":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.ChainConfig.prague_time":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.ChainConfig.verkle_time":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.ChainConfig.osaka_time":
		return protoreflect.ValueOfString("")
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.ChainConfig"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.ChainConfig does not contain field %s", fd.FullName()))
	}
}

// WhichOneof reports which field within the oneof is populated,
// returning nil if none are populated.
// It panics if the oneof descriptor does not belong to this message.
func (x *fastReflection_ChainConfig) WhichOneof(d protoreflect.OneofDescriptor) protoreflect.FieldDescriptor {
	switch d.FullName() {
	default:
		panic(fmt.Errorf("%s is not a oneof field in cosmos.evm.vm.v1.ChainConfig", d.FullName()))
	}
	panic("unreachable")
}

// GetUnknown retrieves the entire list of unknown fields.
// The caller may only mutate the contents of the RawFields
// if the mutated bytes are stored back into the message with SetUnknown.
func (x *fastReflection_ChainConfig) GetUnknown() protoreflect.RawFields {
	return x.unknownFields
}

// SetUnknown stores an entire list of unknown fields.
// The raw fields must be syntactically valid according to the wire format.
// An implementation may panic if this is not the case.
// Once stored, the caller must not mutate the content of the RawFields.
// An empty RawFields may be passed to clear the fields.
//
// SetUnknown is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_ChainConfig) SetUnknown(fields protoreflect.RawFields) {
	x.unknownFields = fields
}

// IsValid reports whether the message is valid.
//
// An invalid message is an empty, read-only value.
//
// An invalid message often corresponds to a nil pointer of the concrete
// message type, but the details are implementation dependent.
// Validity is not part of the protobuf data model, and may not
// be preserved in marshaling or other operations.
func (x *fastReflection_ChainConfig) IsValid() bool {
	return x != nil
}

// ProtoMethods returns optional fastReflectionFeature-path implementations of various operations.
// This method may return nil.
//
// The returned methods type is identical to
// "google.golang.org/protobuf/runtime/protoiface".Methods.
// Consult the protoiface package documentation for details.
func (x *fastReflection_ChainConfig) ProtoMethods() *protoiface.Methods {
	size := func(input protoiface.SizeInput) protoiface.SizeOutput {
		x := input.Message.Interface().(*ChainConfig)
		if x == nil {
			return protoiface.SizeOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Size:              0,
			}
		}
		options := runtime.SizeInputToOptions(input)
		_ = options
		var n int
		var l int
		_ = l
		l = len(x.HomesteadBlock)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		l = len(x.DaoForkBlock)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		if x.DaoForkSupport {
			n += 2
		}
		l = len(x.Eip150Block)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		l = len(x.Eip155Block)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		l = len(x.Eip158Block)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		l = len(x.ByzantiumBlock)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		l = len(x.ConstantinopleBlock)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		l = len(x.PetersburgBlock)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		l = len(x.IstanbulBlock)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		l = len(x.MuirGlacierBlock)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		l = len(x.BerlinBlock)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		l = len(x.LondonBlock)
		if l > 0 {
			n += 2 + l + runtime.Sov(uint64(l))
		}
		l = len(x.ArrowGlacierBlock)
		if l > 0 {
			n += 2 + l + runtime.Sov(uint64(l))
		}
		l = len(x.GrayGlacierBlock)
		if l > 0 {
			n += 2 + l + runtime.Sov(uint64(l))
		}
		l = len(x.MergeNetsplitBlock)
		if l > 0 {
			n += 2 + l + runtime.Sov(uint64(l))
		}
		if x.ChainId != 0 {
			n += 2 + runtime.Sov(uint64(x.ChainId))
		}
		l = len(x.Denom)
		if l > 0 {
			n += 2 + l + runtime.Sov(uint64(l))
		}
		if x.Decimals != 0 {
			n += 2 + runtime.Sov(uint64(x.Decimals))
		}
		l = len(x.ShanghaiTime)
		if l > 0 {
			n += 2 + l + runtime.Sov(uint64(l))
		}
		l = len(x.CancunTime)
		if l > 0 {
			n += 2 + l + runtime.Sov(uint64(l))
		}
		l = len(x.PragueTime)
		if l > 0 {
			n += 2 + l + runtime.Sov(uint64(l))
		}
		l = len(x.VerkleTime)
		if l > 0 {
			n += 2 + l + runtime.Sov(uint64(l))
		}
		l = len(x.OsakaTime)
		if l > 0 {
			n += 2 + l + runtime.Sov(uint64(l))
		}
		if x.unknownFields != nil {
			n += len(x.unknownFields)
		}
		return protoiface.SizeOutput{
			NoUnkeyedLiterals: input.NoUnkeyedLiterals,
			Size:              n,
		}
	}

	marshal := func(input protoiface.MarshalInput) (protoiface.MarshalOutput, error) {
		x := input.Message.Interface().(*ChainConfig)
		if x == nil {
			return protoiface.MarshalOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Buf:               input.Buf,
			}, nil
		}
		options := runtime.MarshalInputToOptions(input)
		_ = options
		size := options.Size(x)
		dAtA := make([]byte, size)
		i := len(dAtA)
		_ = i
		var l int
		_ = l
		if x.unknownFields != nil {
			i -= len(x.unknownFields)
			copy(dAtA[i:], x.unknownFields)
		}
		if len(x.OsakaTime) > 0 {
			i -= len(x.OsakaTime)
			copy(dAtA[i:], x.OsakaTime)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.OsakaTime)))
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xfa
		}
		if len(x.VerkleTime) > 0 {
			i -= len(x.VerkleTime)
			copy(dAtA[i:], x.VerkleTime)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.VerkleTime)))
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xf2
		}
		if len(x.PragueTime) > 0 {
			i -= len(x.PragueTime)
			copy(dAtA[i:], x.PragueTime)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.PragueTime)))
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xea
		}
		if len(x.CancunTime) > 0 {
			i -= len(x.CancunTime)
			copy(dAtA[i:], x.CancunTime)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.CancunTime)))
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xe2
		}
		if len(x.ShanghaiTime) > 0 {
			i -= len(x.ShanghaiTime)
			copy(dAtA[i:], x.ShanghaiTime)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.ShanghaiTime)))
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xda
		}
		if x.Decimals != 0 {
			i = runtime.EncodeVarint(dAtA, i, uint64(x.Decimals))
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xd0
		}
		if len(x.Denom) > 0 {
			i -= len(x.Denom)
			copy(dAtA[i:], x.Denom)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.Denom)))
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xca
		}
		if x.ChainId != 0 {
			i = runtime.EncodeVarint(dAtA, i, uint64(x.ChainId))
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xc0
		}
		if len(x.MergeNetsplitBlock) > 0 {
			i -= len(x.MergeNetsplitBlock)
			copy(dAtA[i:], x.MergeNetsplitBlock)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.MergeNetsplitBlock)))
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xaa
		}
		if len(x.GrayGlacierBlock) > 0 {
			i -= len(x.GrayGlacierBlock)
			copy(dAtA[i:], x.GrayGlacierBlock)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.GrayGlacierBlock)))
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0xa2
		}
		if len(x.ArrowGlacierBlock) > 0 {
			i -= len(x.ArrowGlacierBlock)
			copy(dAtA[i:], x.ArrowGlacierBlock)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.ArrowGlacierBlock)))
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0x92
		}
		if len(x.LondonBlock) > 0 {
			i -= len(x.LondonBlock)
			copy(dAtA[i:], x.LondonBlock)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.LondonBlock)))
			i--
			dAtA[i] = 0x1
			i--
			dAtA[i] = 0x8a
		}
		if len(x.BerlinBlock) > 0 {
			i -= len(x.BerlinBlock)
			copy(dAtA[i:], x.BerlinBlock)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.BerlinBlock)))
			i--
			dAtA[i] = 0x6a
		}
		if len(x.MuirGlacierBlock) > 0 {
			i -= len(x.MuirGlacierBlock)
			copy(dAtA[i:], x.MuirGlacierBlock)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.MuirGlacierBlock)))
			i--
			dAtA[i] = 0x62
		}
		if len(x.IstanbulBlock) > 0 {
			i -= len(x.IstanbulBlock)
			copy(dAtA[i:], x.IstanbulBlock)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.IstanbulBlock)))
			i--
			dAtA[i] = 0x5a
		}
		if len(x.PetersburgBlock) > 0 {
			i -= len(x.PetersburgBlock)
			copy(dAtA[i:], x.PetersburgBlock)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.PetersburgBlock)))
			i--
			dAtA[i] = 0x52
		}
		if len(x.ConstantinopleBlock) > 0 {
			i -= len(x.ConstantinopleBlock)
			copy(dAtA[i:], x.ConstantinopleBlock)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.ConstantinopleBlock)))
			i--
			dAtA[i] = 0x4a
		}
		if len(x.ByzantiumBlock) > 0 {
			i -= len(x.ByzantiumBlock)
			copy(dAtA[i:], x.ByzantiumBlock)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.ByzantiumBlock)))
			i--
			dAtA[i] = 0x42
		}
		if len(x.Eip158Block) > 0 {
			i -= len(x.Eip158Block)
			copy(dAtA[i:], x.Eip158Block)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.Eip158Block)))
			i--
			dAtA[i] = 0x3a
		}
		if len(x.Eip155Block) > 0 {
			i -= len(x.Eip155Block)
			copy(dAtA[i:], x.Eip155Block)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.Eip155Block)))
			i--
			dAtA[i] = 0x32
		}
		if len(x.Eip150Block) > 0 {
			i -= len(x.Eip150Block)
			copy(dAtA[i:], x.Eip150Block)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.Eip150Block)))
			i--
			dAtA[i] = 0x22
		}
		if x.DaoForkSupport {
			i--
			if x.DaoForkSupport {
				dAtA[i] = 1
			} else {
				dAtA[i] = 0
			}
			i--
			dAtA[i] = 0x18
		}
		if len(x.DaoForkBlock) > 0 {
			i -= len(x.DaoForkBlock)
			copy(dAtA[i:], x.DaoForkBlock)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.DaoForkBlock)))
			i--
			dAtA[i] = 0x12
		}
		if len(x.HomesteadBlock) > 0 {
			i -= len(x.HomesteadBlock)
			copy(dAtA[i:], x.HomesteadBlock)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.HomesteadBlock)))
			i--
			dAtA[i] = 0xa
		}
		if input.Buf != nil {
			input.Buf = append(input.Buf, dAtA...)
		} else {
			input.Buf = dAtA
		}
		return protoiface.MarshalOutput{
			NoUnkeyedLiterals: input.NoUnkeyedLiterals,
			Buf:               input.Buf,
		}, nil
	}
	unmarshal := func(input protoiface.UnmarshalInput) (protoiface.UnmarshalOutput, error) {
		x := input.Message.Interface().(*ChainConfig)
		if x == nil {
			return protoiface.UnmarshalOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Flags:             input.Flags,
			}, nil
		}
		options := runtime.UnmarshalInputToOptions(input)
		_ = options
		dAtA := input.Buf
		l := len(dAtA)
		iNdEx := 0
		for iNdEx < l {
			preIndex := iNdEx
			var wire uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
				}
				if iNdEx >= l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				wire |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			fieldNum := int32(wire >> 3)
			wireType := int(wire & 0x7)
			if wireType == 4 {
				return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: ChainConfig: wiretype end group for non-group")
			}
			if fieldNum <= 0 {
				return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: ChainConfig: illegal tag %d (wire type %d)", fieldNum, wire)
			}
			switch fieldNum {
			case 1:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field HomesteadBlock", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.HomesteadBlock = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 2:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field DaoForkBlock", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.DaoForkBlock = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 3:
				if wireType != 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field DaoForkSupport", wireType)
				}
				var v int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				x.DaoForkSupport = bool(v != 0)
			case 4:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Eip150Block", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.Eip150Block = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 6:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Eip155Block", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.Eip155Block = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 7:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Eip158Block", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.Eip158Block = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 8:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field ByzantiumBlock", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.ByzantiumBlock = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 9:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field ConstantinopleBlock", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.ConstantinopleBlock = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 10:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field PetersburgBlock", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.PetersburgBlock = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 11:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field IstanbulBlock", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.IstanbulBlock = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 12:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field MuirGlacierBlock", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.MuirGlacierBlock = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 13:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field BerlinBlock", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.BerlinBlock = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 17:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field LondonBlock", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.LondonBlock = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 18:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field ArrowGlacierBlock", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.ArrowGlacierBlock = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 20:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field GrayGlacierBlock", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.GrayGlacierBlock = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 21:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field MergeNetsplitBlock", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.MergeNetsplitBlock = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 24:
				if wireType != 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field ChainId", wireType)
				}
				x.ChainId = 0
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					x.ChainId |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
			case 25:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Denom", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.Denom = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 26:
				if wireType != 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Decimals", wireType)
				}
				x.Decimals = 0
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					x.Decimals |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
			case 27:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field ShanghaiTime", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.ShanghaiTime = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 28:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field CancunTime", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.CancunTime = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 29:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field PragueTime", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.PragueTime = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 30:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field VerkleTime", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.VerkleTime = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 31:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field OsakaTime", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.OsakaTime = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			default:
				iNdEx = preIndex
				skippy, err := runtime.Skip(dAtA[iNdEx:])
				if err != nil {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, err
				}
				if (skippy < 0) || (iNdEx+skippy) < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if (iNdEx + skippy) > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				if !options.DiscardUnknown {
					x.unknownFields = append(x.unknownFields, dAtA[iNdEx:iNdEx+skippy]...)
				}
				iNdEx += skippy
			}
		}

		if iNdEx > l {
			return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
		}
		return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, nil
	}
	return &protoiface.Methods{
		NoUnkeyedLiterals: struct{}{},
		Flags:             protoiface.SupportMarshalDeterministic | protoiface.SupportUnmarshalDiscardUnknown,
		Size:              size,
		Marshal:           marshal,
		Unmarshal:         unmarshal,
		Merge:             nil,
		CheckInitialized:  nil,
	}
}

var (
	md_State       protoreflect.MessageDescriptor
	fd_State_key   protoreflect.FieldDescriptor
	fd_State_value protoreflect.FieldDescriptor
)

func init() {
	file_cosmos_evm_vm_v1_evm_proto_init()
	md_State = File_cosmos_evm_vm_v1_evm_proto.Messages().ByName("State")
	fd_State_key = md_State.Fields().ByName("key")
	fd_State_value = md_State.Fields().ByName("value")
}

var _ protoreflect.Message = (*fastReflection_State)(nil)

type fastReflection_State State

func (x *State) ProtoReflect() protoreflect.Message {
	return (*fastReflection_State)(x)
}

func (x *State) slowProtoReflect() protoreflect.Message {
	mi := &file_cosmos_evm_vm_v1_evm_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

var _fastReflection_State_messageType fastReflection_State_messageType
var _ protoreflect.MessageType = fastReflection_State_messageType{}

type fastReflection_State_messageType struct{}

func (x fastReflection_State_messageType) Zero() protoreflect.Message {
	return (*fastReflection_State)(nil)
}
func (x fastReflection_State_messageType) New() protoreflect.Message {
	return new(fastReflection_State)
}
func (x fastReflection_State_messageType) Descriptor() protoreflect.MessageDescriptor {
	return md_State
}

// Descriptor returns message descriptor, which contains only the protobuf
// type information for the message.
func (x *fastReflection_State) Descriptor() protoreflect.MessageDescriptor {
	return md_State
}

// Type returns the message type, which encapsulates both Go and protobuf
// type information. If the Go type information is not needed,
// it is recommended that the message descriptor be used instead.
func (x *fastReflection_State) Type() protoreflect.MessageType {
	return _fastReflection_State_messageType
}

// New returns a newly allocated and mutable empty message.
func (x *fastReflection_State) New() protoreflect.Message {
	return new(fastReflection_State)
}

// Interface unwraps the message reflection interface and
// returns the underlying ProtoMessage interface.
func (x *fastReflection_State) Interface() protoreflect.ProtoMessage {
	return (*State)(x)
}

// Range iterates over every populated field in an undefined order,
// calling f for each field descriptor and value encountered.
// Range returns immediately if f returns false.
// While iterating, mutating operations may only be performed
// on the current field descriptor.
func (x *fastReflection_State) Range(f func(protoreflect.FieldDescriptor, protoreflect.Value) bool) {
	if x.Key != "" {
		value := protoreflect.ValueOfString(x.Key)
		if !f(fd_State_key, value) {
			return
		}
	}
	if x.Value != "" {
		value := protoreflect.ValueOfString(x.Value)
		if !f(fd_State_value, value) {
			return
		}
	}
}

// Has reports whether a field is populated.
//
// Some fields have the property of nullability where it is possible to
// distinguish between the default value of a field and whether the field
// was explicitly populated with the default value. Singular message fields,
// member fields of a oneof, and proto2 scalar fields are nullable. Such
// fields are populated only if explicitly set.
//
// In other cases (aside from the nullable cases above),
// a proto3 scalar field is populated if it contains a non-zero value, and
// a repeated field is populated if it is non-empty.
func (x *fastReflection_State) Has(fd protoreflect.FieldDescriptor) bool {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.State.key":
		return x.Key != ""
	case "cosmos.evm.vm.v1.State.value":
		return x.Value != ""
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.State"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.State does not contain field %s", fd.FullName()))
	}
}

// Clear clears the field such that a subsequent Has call reports false.
//
// Clearing an extension field clears both the extension type and value
// associated with the given field number.
//
// Clear is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_State) Clear(fd protoreflect.FieldDescriptor) {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.State.key":
		x.Key = ""
	case "cosmos.evm.vm.v1.State.value":
		x.Value = ""
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.State"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.State does not contain field %s", fd.FullName()))
	}
}

// Get retrieves the value for a field.
//
// For unpopulated scalars, it returns the default value, where
// the default value of a bytes scalar is guaranteed to be a copy.
// For unpopulated composite types, it returns an empty, read-only view
// of the value; to obtain a mutable reference, use Mutable.
func (x *fastReflection_State) Get(descriptor protoreflect.FieldDescriptor) protoreflect.Value {
	switch descriptor.FullName() {
	case "cosmos.evm.vm.v1.State.key":
		value := x.Key
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.State.value":
		value := x.Value
		return protoreflect.ValueOfString(value)
	default:
		if descriptor.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.State"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.State does not contain field %s", descriptor.FullName()))
	}
}

// Set stores the value for a field.
//
// For a field belonging to a oneof, it implicitly clears any other field
// that may be currently set within the same oneof.
// For extension fields, it implicitly stores the provided ExtensionType.
// When setting a composite type, it is unspecified whether the stored value
// aliases the source's memory in any way. If the composite value is an
// empty, read-only value, then it panics.
//
// Set is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_State) Set(fd protoreflect.FieldDescriptor, value protoreflect.Value) {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.State.key":
		x.Key = value.Interface().(string)
	case "cosmos.evm.vm.v1.State.value":
		x.Value = value.Interface().(string)
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.State"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.State does not contain field %s", fd.FullName()))
	}
}

// Mutable returns a mutable reference to a composite type.
//
// If the field is unpopulated, it may allocate a composite value.
// For a field belonging to a oneof, it implicitly clears any other field
// that may be currently set within the same oneof.
// For extension fields, it implicitly stores the provided ExtensionType
// if not already stored.
// It panics if the field does not contain a composite type.
//
// Mutable is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_State) Mutable(fd protoreflect.FieldDescriptor) protoreflect.Value {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.State.key":
		panic(fmt.Errorf("field key of message cosmos.evm.vm.v1.State is not mutable"))
	case "cosmos.evm.vm.v1.State.value":
		panic(fmt.Errorf("field value of message cosmos.evm.vm.v1.State is not mutable"))
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.State"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.State does not contain field %s", fd.FullName()))
	}
}

// NewField returns a new value that is assignable to the field
// for the given descriptor. For scalars, this returns the default value.
// For lists, maps, and messages, this returns a new, empty, mutable value.
func (x *fastReflection_State) NewField(fd protoreflect.FieldDescriptor) protoreflect.Value {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.State.key":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.State.value":
		return protoreflect.ValueOfString("")
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.State"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.State does not contain field %s", fd.FullName()))
	}
}

// WhichOneof reports which field within the oneof is populated,
// returning nil if none are populated.
// It panics if the oneof descriptor does not belong to this message.
func (x *fastReflection_State) WhichOneof(d protoreflect.OneofDescriptor) protoreflect.FieldDescriptor {
	switch d.FullName() {
	default:
		panic(fmt.Errorf("%s is not a oneof field in cosmos.evm.vm.v1.State", d.FullName()))
	}
	panic("unreachable")
}

// GetUnknown retrieves the entire list of unknown fields.
// The caller may only mutate the contents of the RawFields
// if the mutated bytes are stored back into the message with SetUnknown.
func (x *fastReflection_State) GetUnknown() protoreflect.RawFields {
	return x.unknownFields
}

// SetUnknown stores an entire list of unknown fields.
// The raw fields must be syntactically valid according to the wire format.
// An implementation may panic if this is not the case.
// Once stored, the caller must not mutate the content of the RawFields.
// An empty RawFields may be passed to clear the fields.
//
// SetUnknown is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_State) SetUnknown(fields protoreflect.RawFields) {
	x.unknownFields = fields
}

// IsValid reports whether the message is valid.
//
// An invalid message is an empty, read-only value.
//
// An invalid message often corresponds to a nil pointer of the concrete
// message type, but the details are implementation dependent.
// Validity is not part of the protobuf data model, and may not
// be preserved in marshaling or other operations.
func (x *fastReflection_State) IsValid() bool {
	return x != nil
}

// ProtoMethods returns optional fastReflectionFeature-path implementations of various operations.
// This method may return nil.
//
// The returned methods type is identical to
// "google.golang.org/protobuf/runtime/protoiface".Methods.
// Consult the protoiface package documentation for details.
func (x *fastReflection_State) ProtoMethods() *protoiface.Methods {
	size := func(input protoiface.SizeInput) protoiface.SizeOutput {
		x := input.Message.Interface().(*State)
		if x == nil {
			return protoiface.SizeOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Size:              0,
			}
		}
		options := runtime.SizeInputToOptions(input)
		_ = options
		var n int
		var l int
		_ = l
		l = len(x.Key)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		l = len(x.Value)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		if x.unknownFields != nil {
			n += len(x.unknownFields)
		}
		return protoiface.SizeOutput{
			NoUnkeyedLiterals: input.NoUnkeyedLiterals,
			Size:              n,
		}
	}

	marshal := func(input protoiface.MarshalInput) (protoiface.MarshalOutput, error) {
		x := input.Message.Interface().(*State)
		if x == nil {
			return protoiface.MarshalOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Buf:               input.Buf,
			}, nil
		}
		options := runtime.MarshalInputToOptions(input)
		_ = options
		size := options.Size(x)
		dAtA := make([]byte, size)
		i := len(dAtA)
		_ = i
		var l int
		_ = l
		if x.unknownFields != nil {
			i -= len(x.unknownFields)
			copy(dAtA[i:], x.unknownFields)
		}
		if len(x.Value) > 0 {
			i -= len(x.Value)
			copy(dAtA[i:], x.Value)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.Value)))
			i--
			dAtA[i] = 0x12
		}
		if len(x.Key) > 0 {
			i -= len(x.Key)
			copy(dAtA[i:], x.Key)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.Key)))
			i--
			dAtA[i] = 0xa
		}
		if input.Buf != nil {
			input.Buf = append(input.Buf, dAtA...)
		} else {
			input.Buf = dAtA
		}
		return protoiface.MarshalOutput{
			NoUnkeyedLiterals: input.NoUnkeyedLiterals,
			Buf:               input.Buf,
		}, nil
	}
	unmarshal := func(input protoiface.UnmarshalInput) (protoiface.UnmarshalOutput, error) {
		x := input.Message.Interface().(*State)
		if x == nil {
			return protoiface.UnmarshalOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Flags:             input.Flags,
			}, nil
		}
		options := runtime.UnmarshalInputToOptions(input)
		_ = options
		dAtA := input.Buf
		l := len(dAtA)
		iNdEx := 0
		for iNdEx < l {
			preIndex := iNdEx
			var wire uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
				}
				if iNdEx >= l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				wire |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			fieldNum := int32(wire >> 3)
			wireType := int(wire & 0x7)
			if wireType == 4 {
				return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: State: wiretype end group for non-group")
			}
			if fieldNum <= 0 {
				return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: State: illegal tag %d (wire type %d)", fieldNum, wire)
			}
			switch fieldNum {
			case 1:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.Key = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 2:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.Value = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			default:
				iNdEx = preIndex
				skippy, err := runtime.Skip(dAtA[iNdEx:])
				if err != nil {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, err
				}
				if (skippy < 0) || (iNdEx+skippy) < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if (iNdEx + skippy) > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				if !options.DiscardUnknown {
					x.unknownFields = append(x.unknownFields, dAtA[iNdEx:iNdEx+skippy]...)
				}
				iNdEx += skippy
			}
		}

		if iNdEx > l {
			return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
		}
		return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, nil
	}
	return &protoiface.Methods{
		NoUnkeyedLiterals: struct{}{},
		Flags:             protoiface.SupportMarshalDeterministic | protoiface.SupportUnmarshalDiscardUnknown,
		Size:              size,
		Marshal:           marshal,
		Unmarshal:         unmarshal,
		Merge:             nil,
		CheckInitialized:  nil,
	}
}

var _ protoreflect.List = (*_TransactionLogs_2_list)(nil)

type _TransactionLogs_2_list struct {
	list *[]*Log
}

func (x *_TransactionLogs_2_list) Len() int {
	if x.list == nil {
		return 0
	}
	return len(*x.list)
}

func (x *_TransactionLogs_2_list) Get(i int) protoreflect.Value {
	return protoreflect.ValueOfMessage((*x.list)[i].ProtoReflect())
}

func (x *_TransactionLogs_2_list) Set(i int, value protoreflect.Value) {
	valueUnwrapped := value.Message()
	concreteValue := valueUnwrapped.Interface().(*Log)
	(*x.list)[i] = concreteValue
}

func (x *_TransactionLogs_2_list) Append(value protoreflect.Value) {
	valueUnwrapped := value.Message()
	concreteValue := valueUnwrapped.Interface().(*Log)
	*x.list = append(*x.list, concreteValue)
}

func (x *_TransactionLogs_2_list) AppendMutable() protoreflect.Value {
	v := new(Log)
	*x.list = append(*x.list, v)
	return protoreflect.ValueOfMessage(v.ProtoReflect())
}

func (x *_TransactionLogs_2_list) Truncate(n int) {
	for i := n; i < len(*x.list); i++ {
		(*x.list)[i] = nil
	}
	*x.list = (*x.list)[:n]
}

func (x *_TransactionLogs_2_list) NewElement() protoreflect.Value {
	v := new(Log)
	return protoreflect.ValueOfMessage(v.ProtoReflect())
}

func (x *_TransactionLogs_2_list) IsValid() bool {
	return x.list != nil
}

var (
	md_TransactionLogs      protoreflect.MessageDescriptor
	fd_TransactionLogs_hash protoreflect.FieldDescriptor
	fd_TransactionLogs_logs protoreflect.FieldDescriptor
)

func init() {
	file_cosmos_evm_vm_v1_evm_proto_init()
	md_TransactionLogs = File_cosmos_evm_vm_v1_evm_proto.Messages().ByName("TransactionLogs")
	fd_TransactionLogs_hash = md_TransactionLogs.Fields().ByName("hash")
	fd_TransactionLogs_logs = md_TransactionLogs.Fields().ByName("logs")
}

var _ protoreflect.Message = (*fastReflection_TransactionLogs)(nil)

type fastReflection_TransactionLogs TransactionLogs

func (x *TransactionLogs) ProtoReflect() protoreflect.Message {
	return (*fastReflection_TransactionLogs)(x)
}

func (x *TransactionLogs) slowProtoReflect() protoreflect.Message {
	mi := &file_cosmos_evm_vm_v1_evm_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

var _fastReflection_TransactionLogs_messageType fastReflection_TransactionLogs_messageType
var _ protoreflect.MessageType = fastReflection_TransactionLogs_messageType{}

type fastReflection_TransactionLogs_messageType struct{}

func (x fastReflection_TransactionLogs_messageType) Zero() protoreflect.Message {
	return (*fastReflection_TransactionLogs)(nil)
}
func (x fastReflection_TransactionLogs_messageType) New() protoreflect.Message {
	return new(fastReflection_TransactionLogs)
}
func (x fastReflection_TransactionLogs_messageType) Descriptor() protoreflect.MessageDescriptor {
	return md_TransactionLogs
}

// Descriptor returns message descriptor, which contains only the protobuf
// type information for the message.
func (x *fastReflection_TransactionLogs) Descriptor() protoreflect.MessageDescriptor {
	return md_TransactionLogs
}

// Type returns the message type, which encapsulates both Go and protobuf
// type information. If the Go type information is not needed,
// it is recommended that the message descriptor be used instead.
func (x *fastReflection_TransactionLogs) Type() protoreflect.MessageType {
	return _fastReflection_TransactionLogs_messageType
}

// New returns a newly allocated and mutable empty message.
func (x *fastReflection_TransactionLogs) New() protoreflect.Message {
	return new(fastReflection_TransactionLogs)
}

// Interface unwraps the message reflection interface and
// returns the underlying ProtoMessage interface.
func (x *fastReflection_TransactionLogs) Interface() protoreflect.ProtoMessage {
	return (*TransactionLogs)(x)
}

// Range iterates over every populated field in an undefined order,
// calling f for each field descriptor and value encountered.
// Range returns immediately if f returns false.
// While iterating, mutating operations may only be performed
// on the current field descriptor.
func (x *fastReflection_TransactionLogs) Range(f func(protoreflect.FieldDescriptor, protoreflect.Value) bool) {
	if x.Hash != "" {
		value := protoreflect.ValueOfString(x.Hash)
		if !f(fd_TransactionLogs_hash, value) {
			return
		}
	}
	if len(x.Logs) != 0 {
		value := protoreflect.ValueOfList(&_TransactionLogs_2_list{list: &x.Logs})
		if !f(fd_TransactionLogs_logs, value) {
			return
		}
	}
}

// Has reports whether a field is populated.
//
// Some fields have the property of nullability where it is possible to
// distinguish between the default value of a field and whether the field
// was explicitly populated with the default value. Singular message fields,
// member fields of a oneof, and proto2 scalar fields are nullable. Such
// fields are populated only if explicitly set.
//
// In other cases (aside from the nullable cases above),
// a proto3 scalar field is populated if it contains a non-zero value, and
// a repeated field is populated if it is non-empty.
func (x *fastReflection_TransactionLogs) Has(fd protoreflect.FieldDescriptor) bool {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.TransactionLogs.hash":
		return x.Hash != ""
	case "cosmos.evm.vm.v1.TransactionLogs.logs":
		return len(x.Logs) != 0
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.TransactionLogs"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.TransactionLogs does not contain field %s", fd.FullName()))
	}
}

// Clear clears the field such that a subsequent Has call reports false.
//
// Clearing an extension field clears both the extension type and value
// associated with the given field number.
//
// Clear is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_TransactionLogs) Clear(fd protoreflect.FieldDescriptor) {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.TransactionLogs.hash":
		x.Hash = ""
	case "cosmos.evm.vm.v1.TransactionLogs.logs":
		x.Logs = nil
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.TransactionLogs"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.TransactionLogs does not contain field %s", fd.FullName()))
	}
}

// Get retrieves the value for a field.
//
// For unpopulated scalars, it returns the default value, where
// the default value of a bytes scalar is guaranteed to be a copy.
// For unpopulated composite types, it returns an empty, read-only view
// of the value; to obtain a mutable reference, use Mutable.
func (x *fastReflection_TransactionLogs) Get(descriptor protoreflect.FieldDescriptor) protoreflect.Value {
	switch descriptor.FullName() {
	case "cosmos.evm.vm.v1.TransactionLogs.hash":
		value := x.Hash
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.TransactionLogs.logs":
		if len(x.Logs) == 0 {
			return protoreflect.ValueOfList(&_TransactionLogs_2_list{})
		}
		listValue := &_TransactionLogs_2_list{list: &x.Logs}
		return protoreflect.ValueOfList(listValue)
	default:
		if descriptor.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.TransactionLogs"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.TransactionLogs does not contain field %s", descriptor.FullName()))
	}
}

// Set stores the value for a field.
//
// For a field belonging to a oneof, it implicitly clears any other field
// that may be currently set within the same oneof.
// For extension fields, it implicitly stores the provided ExtensionType.
// When setting a composite type, it is unspecified whether the stored value
// aliases the source's memory in any way. If the composite value is an
// empty, read-only value, then it panics.
//
// Set is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_TransactionLogs) Set(fd protoreflect.FieldDescriptor, value protoreflect.Value) {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.TransactionLogs.hash":
		x.Hash = value.Interface().(string)
	case "cosmos.evm.vm.v1.TransactionLogs.logs":
		lv := value.List()
		clv := lv.(*_TransactionLogs_2_list)
		x.Logs = *clv.list
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.TransactionLogs"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.TransactionLogs does not contain field %s", fd.FullName()))
	}
}

// Mutable returns a mutable reference to a composite type.
//
// If the field is unpopulated, it may allocate a composite value.
// For a field belonging to a oneof, it implicitly clears any other field
// that may be currently set within the same oneof.
// For extension fields, it implicitly stores the provided ExtensionType
// if not already stored.
// It panics if the field does not contain a composite type.
//
// Mutable is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_TransactionLogs) Mutable(fd protoreflect.FieldDescriptor) protoreflect.Value {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.TransactionLogs.logs":
		if x.Logs == nil {
			x.Logs = []*Log{}
		}
		value := &_TransactionLogs_2_list{list: &x.Logs}
		return protoreflect.ValueOfList(value)
	case "cosmos.evm.vm.v1.TransactionLogs.hash":
		panic(fmt.Errorf("field hash of message cosmos.evm.vm.v1.TransactionLogs is not mutable"))
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.TransactionLogs"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.TransactionLogs does not contain field %s", fd.FullName()))
	}
}

// NewField returns a new value that is assignable to the field
// for the given descriptor. For scalars, this returns the default value.
// For lists, maps, and messages, this returns a new, empty, mutable value.
func (x *fastReflection_TransactionLogs) NewField(fd protoreflect.FieldDescriptor) protoreflect.Value {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.TransactionLogs.hash":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.TransactionLogs.logs":
		list := []*Log{}
		return protoreflect.ValueOfList(&_TransactionLogs_2_list{list: &list})
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.TransactionLogs"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.TransactionLogs does not contain field %s", fd.FullName()))
	}
}

// WhichOneof reports which field within the oneof is populated,
// returning nil if none are populated.
// It panics if the oneof descriptor does not belong to this message.
func (x *fastReflection_TransactionLogs) WhichOneof(d protoreflect.OneofDescriptor) protoreflect.FieldDescriptor {
	switch d.FullName() {
	default:
		panic(fmt.Errorf("%s is not a oneof field in cosmos.evm.vm.v1.TransactionLogs", d.FullName()))
	}
	panic("unreachable")
}

// GetUnknown retrieves the entire list of unknown fields.
// The caller may only mutate the contents of the RawFields
// if the mutated bytes are stored back into the message with SetUnknown.
func (x *fastReflection_TransactionLogs) GetUnknown() protoreflect.RawFields {
	return x.unknownFields
}

// SetUnknown stores an entire list of unknown fields.
// The raw fields must be syntactically valid according to the wire format.
// An implementation may panic if this is not the case.
// Once stored, the caller must not mutate the content of the RawFields.
// An empty RawFields may be passed to clear the fields.
//
// SetUnknown is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_TransactionLogs) SetUnknown(fields protoreflect.RawFields) {
	x.unknownFields = fields
}

// IsValid reports whether the message is valid.
//
// An invalid message is an empty, read-only value.
//
// An invalid message often corresponds to a nil pointer of the concrete
// message type, but the details are implementation dependent.
// Validity is not part of the protobuf data model, and may not
// be preserved in marshaling or other operations.
func (x *fastReflection_TransactionLogs) IsValid() bool {
	return x != nil
}

// ProtoMethods returns optional fastReflectionFeature-path implementations of various operations.
// This method may return nil.
//
// The returned methods type is identical to
// "google.golang.org/protobuf/runtime/protoiface".Methods.
// Consult the protoiface package documentation for details.
func (x *fastReflection_TransactionLogs) ProtoMethods() *protoiface.Methods {
	size := func(input protoiface.SizeInput) protoiface.SizeOutput {
		x := input.Message.Interface().(*TransactionLogs)
		if x == nil {
			return protoiface.SizeOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Size:              0,
			}
		}
		options := runtime.SizeInputToOptions(input)
		_ = options
		var n int
		var l int
		_ = l
		l = len(x.Hash)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		if len(x.Logs) > 0 {
			for _, e := range x.Logs {
				l = options.Size(e)
				n += 1 + l + runtime.Sov(uint64(l))
			}
		}
		if x.unknownFields != nil {
			n += len(x.unknownFields)
		}
		return protoiface.SizeOutput{
			NoUnkeyedLiterals: input.NoUnkeyedLiterals,
			Size:              n,
		}
	}

	marshal := func(input protoiface.MarshalInput) (protoiface.MarshalOutput, error) {
		x := input.Message.Interface().(*TransactionLogs)
		if x == nil {
			return protoiface.MarshalOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Buf:               input.Buf,
			}, nil
		}
		options := runtime.MarshalInputToOptions(input)
		_ = options
		size := options.Size(x)
		dAtA := make([]byte, size)
		i := len(dAtA)
		_ = i
		var l int
		_ = l
		if x.unknownFields != nil {
			i -= len(x.unknownFields)
			copy(dAtA[i:], x.unknownFields)
		}
		if len(x.Logs) > 0 {
			for iNdEx := len(x.Logs) - 1; iNdEx >= 0; iNdEx-- {
				encoded, err := options.Marshal(x.Logs[iNdEx])
				if err != nil {
					return protoiface.MarshalOutput{
						NoUnkeyedLiterals: input.NoUnkeyedLiterals,
						Buf:               input.Buf,
					}, err
				}
				i -= len(encoded)
				copy(dAtA[i:], encoded)
				i = runtime.EncodeVarint(dAtA, i, uint64(len(encoded)))
				i--
				dAtA[i] = 0x12
			}
		}
		if len(x.Hash) > 0 {
			i -= len(x.Hash)
			copy(dAtA[i:], x.Hash)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.Hash)))
			i--
			dAtA[i] = 0xa
		}
		if input.Buf != nil {
			input.Buf = append(input.Buf, dAtA...)
		} else {
			input.Buf = dAtA
		}
		return protoiface.MarshalOutput{
			NoUnkeyedLiterals: input.NoUnkeyedLiterals,
			Buf:               input.Buf,
		}, nil
	}
	unmarshal := func(input protoiface.UnmarshalInput) (protoiface.UnmarshalOutput, error) {
		x := input.Message.Interface().(*TransactionLogs)
		if x == nil {
			return protoiface.UnmarshalOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Flags:             input.Flags,
			}, nil
		}
		options := runtime.UnmarshalInputToOptions(input)
		_ = options
		dAtA := input.Buf
		l := len(dAtA)
		iNdEx := 0
		for iNdEx < l {
			preIndex := iNdEx
			var wire uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
				}
				if iNdEx >= l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				wire |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			fieldNum := int32(wire >> 3)
			wireType := int(wire & 0x7)
			if wireType == 4 {
				return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: TransactionLogs: wiretype end group for non-group")
			}
			if fieldNum <= 0 {
				return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: TransactionLogs: illegal tag %d (wire type %d)", fieldNum, wire)
			}
			switch fieldNum {
			case 1:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Hash", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.Hash = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 2:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Logs", wireType)
				}
				var msglen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					msglen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if msglen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + msglen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.Logs = append(x.Logs, &Log{})
				if err := options.Unmarshal(dAtA[iNdEx:postIndex], x.Logs[len(x.Logs)-1]); err != nil {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, err
				}
				iNdEx = postIndex
			default:
				iNdEx = preIndex
				skippy, err := runtime.Skip(dAtA[iNdEx:])
				if err != nil {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, err
				}
				if (skippy < 0) || (iNdEx+skippy) < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if (iNdEx + skippy) > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				if !options.DiscardUnknown {
					x.unknownFields = append(x.unknownFields, dAtA[iNdEx:iNdEx+skippy]...)
				}
				iNdEx += skippy
			}
		}

		if iNdEx > l {
			return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
		}
		return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, nil
	}
	return &protoiface.Methods{
		NoUnkeyedLiterals: struct{}{},
		Flags:             protoiface.SupportMarshalDeterministic | protoiface.SupportUnmarshalDiscardUnknown,
		Size:              size,
		Marshal:           marshal,
		Unmarshal:         unmarshal,
		Merge:             nil,
		CheckInitialized:  nil,
	}
}

var _ protoreflect.List = (*_Log_2_list)(nil)

type _Log_2_list struct {
	list *[]string
}

func (x *_Log_2_list) Len() int {
	if x.list == nil {
		return 0
	}
	return len(*x.list)
}

func (x *_Log_2_list) Get(i int) protoreflect.Value {
	return protoreflect.ValueOfString((*x.list)[i])
}

func (x *_Log_2_list) Set(i int, value protoreflect.Value) {
	valueUnwrapped := value.String()
	concreteValue := valueUnwrapped
	(*x.list)[i] = concreteValue
}

func (x *_Log_2_list) Append(value protoreflect.Value) {
	valueUnwrapped := value.String()
	concreteValue := valueUnwrapped
	*x.list = append(*x.list, concreteValue)
}

func (x *_Log_2_list) AppendMutable() protoreflect.Value {
	panic(fmt.Errorf("AppendMutable can not be called on message Log at list field Topics as it is not of Message kind"))
}

func (x *_Log_2_list) Truncate(n int) {
	*x.list = (*x.list)[:n]
}

func (x *_Log_2_list) NewElement() protoreflect.Value {
	v := ""
	return protoreflect.ValueOfString(v)
}

func (x *_Log_2_list) IsValid() bool {
	return x.list != nil
}

var (
	md_Log                 protoreflect.MessageDescriptor
	fd_Log_address         protoreflect.FieldDescriptor
	fd_Log_topics          protoreflect.FieldDescriptor
	fd_Log_data            protoreflect.FieldDescriptor
	fd_Log_block_number    protoreflect.FieldDescriptor
	fd_Log_tx_hash         protoreflect.FieldDescriptor
	fd_Log_tx_index        protoreflect.FieldDescriptor
	fd_Log_block_hash      protoreflect.FieldDescriptor
	fd_Log_index           protoreflect.FieldDescriptor
	fd_Log_removed         protoreflect.FieldDescriptor
	fd_Log_block_timestamp protoreflect.FieldDescriptor
)

func init() {
	file_cosmos_evm_vm_v1_evm_proto_init()
	md_Log = File_cosmos_evm_vm_v1_evm_proto.Messages().ByName("Log")
	fd_Log_address = md_Log.Fields().ByName("address")
	fd_Log_topics = md_Log.Fields().ByName("topics")
	fd_Log_data = md_Log.Fields().ByName("data")
	fd_Log_block_number = md_Log.Fields().ByName("block_number")
	fd_Log_tx_hash = md_Log.Fields().ByName("tx_hash")
	fd_Log_tx_index = md_Log.Fields().ByName("tx_index")
	fd_Log_block_hash = md_Log.Fields().ByName("block_hash")
	fd_Log_index = md_Log.Fields().ByName("index")
	fd_Log_removed = md_Log.Fields().ByName("removed")
	fd_Log_block_timestamp = md_Log.Fields().ByName("block_timestamp")
}

var _ protoreflect.Message = (*fastReflection_Log)(nil)

type fastReflection_Log Log

func (x *Log) ProtoReflect() protoreflect.Message {
	return (*fastReflection_Log)(x)
}

func (x *Log) slowProtoReflect() protoreflect.Message {
	mi := &file_cosmos_evm_vm_v1_evm_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

var _fastReflection_Log_messageType fastReflection_Log_messageType
var _ protoreflect.MessageType = fastReflection_Log_messageType{}

type fastReflection_Log_messageType struct{}

func (x fastReflection_Log_messageType) Zero() protoreflect.Message {
	return (*fastReflection_Log)(nil)
}
func (x fastReflection_Log_messageType) New() protoreflect.Message {
	return new(fastReflection_Log)
}
func (x fastReflection_Log_messageType) Descriptor() protoreflect.MessageDescriptor {
	return md_Log
}

// Descriptor returns message descriptor, which contains only the protobuf
// type information for the message.
func (x *fastReflection_Log) Descriptor() protoreflect.MessageDescriptor {
	return md_Log
}

// Type returns the message type, which encapsulates both Go and protobuf
// type information. If the Go type information is not needed,
// it is recommended that the message descriptor be used instead.
func (x *fastReflection_Log) Type() protoreflect.MessageType {
	return _fastReflection_Log_messageType
}

// New returns a newly allocated and mutable empty message.
func (x *fastReflection_Log) New() protoreflect.Message {
	return new(fastReflection_Log)
}

// Interface unwraps the message reflection interface and
// returns the underlying ProtoMessage interface.
func (x *fastReflection_Log) Interface() protoreflect.ProtoMessage {
	return (*Log)(x)
}

// Range iterates over every populated field in an undefined order,
// calling f for each field descriptor and value encountered.
// Range returns immediately if f returns false.
// While iterating, mutating operations may only be performed
// on the current field descriptor.
func (x *fastReflection_Log) Range(f func(protoreflect.FieldDescriptor, protoreflect.Value) bool) {
	if x.Address != "" {
		value := protoreflect.ValueOfString(x.Address)
		if !f(fd_Log_address, value) {
			return
		}
	}
	if len(x.Topics) != 0 {
		value := protoreflect.ValueOfList(&_Log_2_list{list: &x.Topics})
		if !f(fd_Log_topics, value) {
			return
		}
	}
	if len(x.Data) != 0 {
		value := protoreflect.ValueOfBytes(x.Data)
		if !f(fd_Log_data, value) {
			return
		}
	}
	if x.BlockNumber != uint64(0) {
		value := protoreflect.ValueOfUint64(x.BlockNumber)
		if !f(fd_Log_block_number, value) {
			return
		}
	}
	if x.TxHash != "" {
		value := protoreflect.ValueOfString(x.TxHash)
		if !f(fd_Log_tx_hash, value) {
			return
		}
	}
	if x.TxIndex != uint64(0) {
		value := protoreflect.ValueOfUint64(x.TxIndex)
		if !f(fd_Log_tx_index, value) {
			return
		}
	}
	if x.BlockHash != "" {
		value := protoreflect.ValueOfString(x.BlockHash)
		if !f(fd_Log_block_hash, value) {
			return
		}
	}
	if x.Index != uint64(0) {
		value := protoreflect.ValueOfUint64(x.Index)
		if !f(fd_Log_index, value) {
			return
		}
	}
	if x.Removed != false {
		value := protoreflect.ValueOfBool(x.Removed)
		if !f(fd_Log_removed, value) {
			return
		}
	}
	if x.BlockTimestamp != uint64(0) {
		value := protoreflect.ValueOfUint64(x.BlockTimestamp)
		if !f(fd_Log_block_timestamp, value) {
			return
		}
	}
}

// Has reports whether a field is populated.
//
// Some fields have the property of nullability where it is possible to
// distinguish between the default value of a field and whether the field
// was explicitly populated with the default value. Singular message fields,
// member fields of a oneof, and proto2 scalar fields are nullable. Such
// fields are populated only if explicitly set.
//
// In other cases (aside from the nullable cases above),
// a proto3 scalar field is populated if it contains a non-zero value, and
// a repeated field is populated if it is non-empty.
func (x *fastReflection_Log) Has(fd protoreflect.FieldDescriptor) bool {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.Log.address":
		return x.Address != ""
	case "cosmos.evm.vm.v1.Log.topics":
		return len(x.Topics) != 0
	case "cosmos.evm.vm.v1.Log.data":
		return len(x.Data) != 0
	case "cosmos.evm.vm.v1.Log.block_number":
		return x.BlockNumber != uint64(0)
	case "cosmos.evm.vm.v1.Log.tx_hash":
		return x.TxHash != ""
	case "cosmos.evm.vm.v1.Log.tx_index":
		return x.TxIndex != uint64(0)
	case "cosmos.evm.vm.v1.Log.block_hash":
		return x.BlockHash != ""
	case "cosmos.evm.vm.v1.Log.index":
		return x.Index != uint64(0)
	case "cosmos.evm.vm.v1.Log.removed":
		return x.Removed != false
	case "cosmos.evm.vm.v1.Log.block_timestamp":
		return x.BlockTimestamp != uint64(0)
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.Log"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.Log does not contain field %s", fd.FullName()))
	}
}

// Clear clears the field such that a subsequent Has call reports false.
//
// Clearing an extension field clears both the extension type and value
// associated with the given field number.
//
// Clear is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_Log) Clear(fd protoreflect.FieldDescriptor) {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.Log.address":
		x.Address = ""
	case "cosmos.evm.vm.v1.Log.topics":
		x.Topics = nil
	case "cosmos.evm.vm.v1.Log.data":
		x.Data = nil
	case "cosmos.evm.vm.v1.Log.block_number":
		x.BlockNumber = uint64(0)
	case "cosmos.evm.vm.v1.Log.tx_hash":
		x.TxHash = ""
	case "cosmos.evm.vm.v1.Log.tx_index":
		x.TxIndex = uint64(0)
	case "cosmos.evm.vm.v1.Log.block_hash":
		x.BlockHash = ""
	case "cosmos.evm.vm.v1.Log.index":
		x.Index = uint64(0)
	case "cosmos.evm.vm.v1.Log.removed":
		x.Removed = false
	case "cosmos.evm.vm.v1.Log.block_timestamp":
		x.BlockTimestamp = uint64(0)
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.Log"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.Log does not contain field %s", fd.FullName()))
	}
}

// Get retrieves the value for a field.
//
// For unpopulated scalars, it returns the default value, where
// the default value of a bytes scalar is guaranteed to be a copy.
// For unpopulated composite types, it returns an empty, read-only view
// of the value; to obtain a mutable reference, use Mutable.
func (x *fastReflection_Log) Get(descriptor protoreflect.FieldDescriptor) protoreflect.Value {
	switch descriptor.FullName() {
	case "cosmos.evm.vm.v1.Log.address":
		value := x.Address
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.Log.topics":
		if len(x.Topics) == 0 {
			return protoreflect.ValueOfList(&_Log_2_list{})
		}
		listValue := &_Log_2_list{list: &x.Topics}
		return protoreflect.ValueOfList(listValue)
	case "cosmos.evm.vm.v1.Log.data":
		value := x.Data
		return protoreflect.ValueOfBytes(value)
	case "cosmos.evm.vm.v1.Log.block_number":
		value := x.BlockNumber
		return protoreflect.ValueOfUint64(value)
	case "cosmos.evm.vm.v1.Log.tx_hash":
		value := x.TxHash
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.Log.tx_index":
		value := x.TxIndex
		return protoreflect.ValueOfUint64(value)
	case "cosmos.evm.vm.v1.Log.block_hash":
		value := x.BlockHash
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.Log.index":
		value := x.Index
		return protoreflect.ValueOfUint64(value)
	case "cosmos.evm.vm.v1.Log.removed":
		value := x.Removed
		return protoreflect.ValueOfBool(value)
	case "cosmos.evm.vm.v1.Log.block_timestamp":
		value := x.BlockTimestamp
		return protoreflect.ValueOfUint64(value)
	default:
		if descriptor.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.Log"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.Log does not contain field %s", descriptor.FullName()))
	}
}

// Set stores the value for a field.
//
// For a field belonging to a oneof, it implicitly clears any other field
// that may be currently set within the same oneof.
// For extension fields, it implicitly stores the provided ExtensionType.
// When setting a composite type, it is unspecified whether the stored value
// aliases the source's memory in any way. If the composite value is an
// empty, read-only value, then it panics.
//
// Set is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_Log) Set(fd protoreflect.FieldDescriptor, value protoreflect.Value) {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.Log.address":
		x.Address = value.Interface().(string)
	case "cosmos.evm.vm.v1.Log.topics":
		lv := value.List()
		clv := lv.(*_Log_2_list)
		x.Topics = *clv.list
	case "cosmos.evm.vm.v1.Log.data":
		x.Data = value.Bytes()
	case "cosmos.evm.vm.v1.Log.block_number":
		x.BlockNumber = value.Uint()
	case "cosmos.evm.vm.v1.Log.tx_hash":
		x.TxHash = value.Interface().(string)
	case "cosmos.evm.vm.v1.Log.tx_index":
		x.TxIndex = value.Uint()
	case "cosmos.evm.vm.v1.Log.block_hash":
		x.BlockHash = value.Interface().(string)
	case "cosmos.evm.vm.v1.Log.index":
		x.Index = value.Uint()
	case "cosmos.evm.vm.v1.Log.removed":
		x.Removed = value.Bool()
	case "cosmos.evm.vm.v1.Log.block_timestamp":
		x.BlockTimestamp = value.Uint()
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.Log"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.Log does not contain field %s", fd.FullName()))
	}
}

// Mutable returns a mutable reference to a composite type.
//
// If the field is unpopulated, it may allocate a composite value.
// For a field belonging to a oneof, it implicitly clears any other field
// that may be currently set within the same oneof.
// For extension fields, it implicitly stores the provided ExtensionType
// if not already stored.
// It panics if the field does not contain a composite type.
//
// Mutable is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_Log) Mutable(fd protoreflect.FieldDescriptor) protoreflect.Value {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.Log.topics":
		if x.Topics == nil {
			x.Topics = []string{}
		}
		value := &_Log_2_list{list: &x.Topics}
		return protoreflect.ValueOfList(value)
	case "cosmos.evm.vm.v1.Log.address":
		panic(fmt.Errorf("field address of message cosmos.evm.vm.v1.Log is not mutable"))
	case "cosmos.evm.vm.v1.Log.data":
		panic(fmt.Errorf("field data of message cosmos.evm.vm.v1.Log is not mutable"))
	case "cosmos.evm.vm.v1.Log.block_number":
		panic(fmt.Errorf("field block_number of message cosmos.evm.vm.v1.Log is not mutable"))
	case "cosmos.evm.vm.v1.Log.tx_hash":
		panic(fmt.Errorf("field tx_hash of message cosmos.evm.vm.v1.Log is not mutable"))
	case "cosmos.evm.vm.v1.Log.tx_index":
		panic(fmt.Errorf("field tx_index of message cosmos.evm.vm.v1.Log is not mutable"))
	case "cosmos.evm.vm.v1.Log.block_hash":
		panic(fmt.Errorf("field block_hash of message cosmos.evm.vm.v1.Log is not mutable"))
	case "cosmos.evm.vm.v1.Log.index":
		panic(fmt.Errorf("field index of message cosmos.evm.vm.v1.Log is not mutable"))
	case "cosmos.evm.vm.v1.Log.removed":
		panic(fmt.Errorf("field removed of message cosmos.evm.vm.v1.Log is not mutable"))
	case "cosmos.evm.vm.v1.Log.block_timestamp":
		panic(fmt.Errorf("field block_timestamp of message cosmos.evm.vm.v1.Log is not mutable"))
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.Log"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.Log does not contain field %s", fd.FullName()))
	}
}

// NewField returns a new value that is assignable to the field
// for the given descriptor. For scalars, this returns the default value.
// For lists, maps, and messages, this returns a new, empty, mutable value.
func (x *fastReflection_Log) NewField(fd protoreflect.FieldDescriptor) protoreflect.Value {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.Log.address":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.Log.topics":
		list := []string{}
		return protoreflect.ValueOfList(&_Log_2_list{list: &list})
	case "cosmos.evm.vm.v1.Log.data":
		return protoreflect.ValueOfBytes(nil)
	case "cosmos.evm.vm.v1.Log.block_number":
		return protoreflect.ValueOfUint64(uint64(0))
	case "cosmos.evm.vm.v1.Log.tx_hash":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.Log.tx_index":
		return protoreflect.ValueOfUint64(uint64(0))
	case "cosmos.evm.vm.v1.Log.block_hash":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.Log.index":
		return protoreflect.ValueOfUint64(uint64(0))
	case "cosmos.evm.vm.v1.Log.removed":
		return protoreflect.ValueOfBool(false)
	case "cosmos.evm.vm.v1.Log.block_timestamp":
		return protoreflect.ValueOfUint64(uint64(0))
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.Log"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.Log does not contain field %s", fd.FullName()))
	}
}

// WhichOneof reports which field within the oneof is populated,
// returning nil if none are populated.
// It panics if the oneof descriptor does not belong to this message.
func (x *fastReflection_Log) WhichOneof(d protoreflect.OneofDescriptor) protoreflect.FieldDescriptor {
	switch d.FullName() {
	default:
		panic(fmt.Errorf("%s is not a oneof field in cosmos.evm.vm.v1.Log", d.FullName()))
	}
	panic("unreachable")
}

// GetUnknown retrieves the entire list of unknown fields.
// The caller may only mutate the contents of the RawFields
// if the mutated bytes are stored back into the message with SetUnknown.
func (x *fastReflection_Log) GetUnknown() protoreflect.RawFields {
	return x.unknownFields
}

// SetUnknown stores an entire list of unknown fields.
// The raw fields must be syntactically valid according to the wire format.
// An implementation may panic if this is not the case.
// Once stored, the caller must not mutate the content of the RawFields.
// An empty RawFields may be passed to clear the fields.
//
// SetUnknown is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_Log) SetUnknown(fields protoreflect.RawFields) {
	x.unknownFields = fields
}

// IsValid reports whether the message is valid.
//
// An invalid message is an empty, read-only value.
//
// An invalid message often corresponds to a nil pointer of the concrete
// message type, but the details are implementation dependent.
// Validity is not part of the protobuf data model, and may not
// be preserved in marshaling or other operations.
func (x *fastReflection_Log) IsValid() bool {
	return x != nil
}

// ProtoMethods returns optional fastReflectionFeature-path implementations of various operations.
// This method may return nil.
//
// The returned methods type is identical to
// "google.golang.org/protobuf/runtime/protoiface".Methods.
// Consult the protoiface package documentation for details.
func (x *fastReflection_Log) ProtoMethods() *protoiface.Methods {
	size := func(input protoiface.SizeInput) protoiface.SizeOutput {
		x := input.Message.Interface().(*Log)
		if x == nil {
			return protoiface.SizeOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Size:              0,
			}
		}
		options := runtime.SizeInputToOptions(input)
		_ = options
		var n int
		var l int
		_ = l
		l = len(x.Address)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		if len(x.Topics) > 0 {
			for _, s := range x.Topics {
				l = len(s)
				n += 1 + l + runtime.Sov(uint64(l))
			}
		}
		l = len(x.Data)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		if x.BlockNumber != 0 {
			n += 1 + runtime.Sov(uint64(x.BlockNumber))
		}
		l = len(x.TxHash)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		if x.TxIndex != 0 {
			n += 1 + runtime.Sov(uint64(x.TxIndex))
		}
		l = len(x.BlockHash)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		if x.Index != 0 {
			n += 1 + runtime.Sov(uint64(x.Index))
		}
		if x.Removed {
			n += 2
		}
		if x.BlockTimestamp != 0 {
			n += 1 + runtime.Sov(uint64(x.BlockTimestamp))
		}
		if x.unknownFields != nil {
			n += len(x.unknownFields)
		}
		return protoiface.SizeOutput{
			NoUnkeyedLiterals: input.NoUnkeyedLiterals,
			Size:              n,
		}
	}

	marshal := func(input protoiface.MarshalInput) (protoiface.MarshalOutput, error) {
		x := input.Message.Interface().(*Log)
		if x == nil {
			return protoiface.MarshalOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Buf:               input.Buf,
			}, nil
		}
		options := runtime.MarshalInputToOptions(input)
		_ = options
		size := options.Size(x)
		dAtA := make([]byte, size)
		i := len(dAtA)
		_ = i
		var l int
		_ = l
		if x.unknownFields != nil {
			i -= len(x.unknownFields)
			copy(dAtA[i:], x.unknownFields)
		}
		if x.BlockTimestamp != 0 {
			i = runtime.EncodeVarint(dAtA, i, uint64(x.BlockTimestamp))
			i--
			dAtA[i] = 0x50
		}
		if x.Removed {
			i--
			if x.Removed {
				dAtA[i] = 1
			} else {
				dAtA[i] = 0
			}
			i--
			dAtA[i] = 0x48
		}
		if x.Index != 0 {
			i = runtime.EncodeVarint(dAtA, i, uint64(x.Index))
			i--
			dAtA[i] = 0x40
		}
		if len(x.BlockHash) > 0 {
			i -= len(x.BlockHash)
			copy(dAtA[i:], x.BlockHash)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.BlockHash)))
			i--
			dAtA[i] = 0x3a
		}
		if x.TxIndex != 0 {
			i = runtime.EncodeVarint(dAtA, i, uint64(x.TxIndex))
			i--
			dAtA[i] = 0x30
		}
		if len(x.TxHash) > 0 {
			i -= len(x.TxHash)
			copy(dAtA[i:], x.TxHash)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.TxHash)))
			i--
			dAtA[i] = 0x2a
		}
		if x.BlockNumber != 0 {
			i = runtime.EncodeVarint(dAtA, i, uint64(x.BlockNumber))
			i--
			dAtA[i] = 0x20
		}
		if len(x.Data) > 0 {
			i -= len(x.Data)
			copy(dAtA[i:], x.Data)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.Data)))
			i--
			dAtA[i] = 0x1a
		}
		if len(x.Topics) > 0 {
			for iNdEx := len(x.Topics) - 1; iNdEx >= 0; iNdEx-- {
				i -= len(x.Topics[iNdEx])
				copy(dAtA[i:], x.Topics[iNdEx])
				i = runtime.EncodeVarint(dAtA, i, uint64(len(x.Topics[iNdEx])))
				i--
				dAtA[i] = 0x12
			}
		}
		if len(x.Address) > 0 {
			i -= len(x.Address)
			copy(dAtA[i:], x.Address)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.Address)))
			i--
			dAtA[i] = 0xa
		}
		if input.Buf != nil {
			input.Buf = append(input.Buf, dAtA...)
		} else {
			input.Buf = dAtA
		}
		return protoiface.MarshalOutput{
			NoUnkeyedLiterals: input.NoUnkeyedLiterals,
			Buf:               input.Buf,
		}, nil
	}
	unmarshal := func(input protoiface.UnmarshalInput) (protoiface.UnmarshalOutput, error) {
		x := input.Message.Interface().(*Log)
		if x == nil {
			return protoiface.UnmarshalOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Flags:             input.Flags,
			}, nil
		}
		options := runtime.UnmarshalInputToOptions(input)
		_ = options
		dAtA := input.Buf
		l := len(dAtA)
		iNdEx := 0
		for iNdEx < l {
			preIndex := iNdEx
			var wire uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
				}
				if iNdEx >= l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				wire |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			fieldNum := int32(wire >> 3)
			wireType := int(wire & 0x7)
			if wireType == 4 {
				return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: Log: wiretype end group for non-group")
			}
			if fieldNum <= 0 {
				return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: Log: illegal tag %d (wire type %d)", fieldNum, wire)
			}
			switch fieldNum {
			case 1:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Address", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.Address = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 2:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Topics", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.Topics = append(x.Topics, string(dAtA[iNdEx:postIndex]))
				iNdEx = postIndex
			case 3:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
				}
				var byteLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					byteLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if byteLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + byteLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.Data = append(x.Data[:0], dAtA[iNdEx:postIndex]...)
				if x.Data == nil {
					x.Data = []byte{}
				}
				iNdEx = postIndex
			case 4:
				if wireType != 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field BlockNumber", wireType)
				}
				x.BlockNumber = 0
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					x.BlockNumber |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
			case 5:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field TxHash", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.TxHash = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 6:
				if wireType != 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field TxIndex", wireType)
				}
				x.TxIndex = 0
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					x.TxIndex |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
			case 7:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field BlockHash", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.BlockHash = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 8:
				if wireType != 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Index", wireType)
				}
				x.Index = 0
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					x.Index |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
			case 9:
				if wireType != 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Removed", wireType)
				}
				var v int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				x.Removed = bool(v != 0)
			case 10:
				if wireType != 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field BlockTimestamp", wireType)
				}
				x.BlockTimestamp = 0
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					x.BlockTimestamp |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
			default:
				iNdEx = preIndex
				skippy, err := runtime.Skip(dAtA[iNdEx:])
				if err != nil {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, err
				}
				if (skippy < 0) || (iNdEx+skippy) < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if (iNdEx + skippy) > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				if !options.DiscardUnknown {
					x.unknownFields = append(x.unknownFields, dAtA[iNdEx:iNdEx+skippy]...)
				}
				iNdEx += skippy
			}
		}

		if iNdEx > l {
			return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
		}
		return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, nil
	}
	return &protoiface.Methods{
		NoUnkeyedLiterals: struct{}{},
		Flags:             protoiface.SupportMarshalDeterministic | protoiface.SupportUnmarshalDiscardUnknown,
		Size:              size,
		Marshal:           marshal,
		Unmarshal:         unmarshal,
		Merge:             nil,
		CheckInitialized:  nil,
	}
}

var (
	md_TxResult                  protoreflect.MessageDescriptor
	fd_TxResult_contract_address protoreflect.FieldDescriptor
	fd_TxResult_bloom            protoreflect.FieldDescriptor
	fd_TxResult_tx_logs          protoreflect.FieldDescriptor
	fd_TxResult_ret              protoreflect.FieldDescriptor
	fd_TxResult_reverted         protoreflect.FieldDescriptor
	fd_TxResult_gas_used         protoreflect.FieldDescriptor
)

func init() {
	file_cosmos_evm_vm_v1_evm_proto_init()
	md_TxResult = File_cosmos_evm_vm_v1_evm_proto.Messages().ByName("TxResult")
	fd_TxResult_contract_address = md_TxResult.Fields().ByName("contract_address")
	fd_TxResult_bloom = md_TxResult.Fields().ByName("bloom")
	fd_TxResult_tx_logs = md_TxResult.Fields().ByName("tx_logs")
	fd_TxResult_ret = md_TxResult.Fields().ByName("ret")
	fd_TxResult_reverted = md_TxResult.Fields().ByName("reverted")
	fd_TxResult_gas_used = md_TxResult.Fields().ByName("gas_used")
}

var _ protoreflect.Message = (*fastReflection_TxResult)(nil)

type fastReflection_TxResult TxResult

func (x *TxResult) ProtoReflect() protoreflect.Message {
	return (*fastReflection_TxResult)(x)
}

func (x *TxResult) slowProtoReflect() protoreflect.Message {
	mi := &file_cosmos_evm_vm_v1_evm_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

var _fastReflection_TxResult_messageType fastReflection_TxResult_messageType
var _ protoreflect.MessageType = fastReflection_TxResult_messageType{}

type fastReflection_TxResult_messageType struct{}

func (x fastReflection_TxResult_messageType) Zero() protoreflect.Message {
	return (*fastReflection_TxResult)(nil)
}
func (x fastReflection_TxResult_messageType) New() protoreflect.Message {
	return new(fastReflection_TxResult)
}
func (x fastReflection_TxResult_messageType) Descriptor() protoreflect.MessageDescriptor {
	return md_TxResult
}

// Descriptor returns message descriptor, which contains only the protobuf
// type information for the message.
func (x *fastReflection_TxResult) Descriptor() protoreflect.MessageDescriptor {
	return md_TxResult
}

// Type returns the message type, which encapsulates both Go and protobuf
// type information. If the Go type information is not needed,
// it is recommended that the message descriptor be used instead.
func (x *fastReflection_TxResult) Type() protoreflect.MessageType {
	return _fastReflection_TxResult_messageType
}

// New returns a newly allocated and mutable empty message.
func (x *fastReflection_TxResult) New() protoreflect.Message {
	return new(fastReflection_TxResult)
}

// Interface unwraps the message reflection interface and
// returns the underlying ProtoMessage interface.
func (x *fastReflection_TxResult) Interface() protoreflect.ProtoMessage {
	return (*TxResult)(x)
}

// Range iterates over every populated field in an undefined order,
// calling f for each field descriptor and value encountered.
// Range returns immediately if f returns false.
// While iterating, mutating operations may only be performed
// on the current field descriptor.
func (x *fastReflection_TxResult) Range(f func(protoreflect.FieldDescriptor, protoreflect.Value) bool) {
	if x.ContractAddress != "" {
		value := protoreflect.ValueOfString(x.ContractAddress)
		if !f(fd_TxResult_contract_address, value) {
			return
		}
	}
	if len(x.Bloom) != 0 {
		value := protoreflect.ValueOfBytes(x.Bloom)
		if !f(fd_TxResult_bloom, value) {
			return
		}
	}
	if x.TxLogs != nil {
		value := protoreflect.ValueOfMessage(x.TxLogs.ProtoReflect())
		if !f(fd_TxResult_tx_logs, value) {
			return
		}
	}
	if len(x.Ret) != 0 {
		value := protoreflect.ValueOfBytes(x.Ret)
		if !f(fd_TxResult_ret, value) {
			return
		}
	}
	if x.Reverted != false {
		value := protoreflect.ValueOfBool(x.Reverted)
		if !f(fd_TxResult_reverted, value) {
			return
		}
	}
	if x.GasUsed != uint64(0) {
		value := protoreflect.ValueOfUint64(x.GasUsed)
		if !f(fd_TxResult_gas_used, value) {
			return
		}
	}
}

// Has reports whether a field is populated.
//
// Some fields have the property of nullability where it is possible to
// distinguish between the default value of a field and whether the field
// was explicitly populated with the default value. Singular message fields,
// member fields of a oneof, and proto2 scalar fields are nullable. Such
// fields are populated only if explicitly set.
//
// In other cases (aside from the nullable cases above),
// a proto3 scalar field is populated if it contains a non-zero value, and
// a repeated field is populated if it is non-empty.
func (x *fastReflection_TxResult) Has(fd protoreflect.FieldDescriptor) bool {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.TxResult.contract_address":
		return x.ContractAddress != ""
	case "cosmos.evm.vm.v1.TxResult.bloom":
		return len(x.Bloom) != 0
	case "cosmos.evm.vm.v1.TxResult.tx_logs":
		return x.TxLogs != nil
	case "cosmos.evm.vm.v1.TxResult.ret":
		return len(x.Ret) != 0
	case "cosmos.evm.vm.v1.TxResult.reverted":
		return x.Reverted != false
	case "cosmos.evm.vm.v1.TxResult.gas_used":
		return x.GasUsed != uint64(0)
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.TxResult"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.TxResult does not contain field %s", fd.FullName()))
	}
}

// Clear clears the field such that a subsequent Has call reports false.
//
// Clearing an extension field clears both the extension type and value
// associated with the given field number.
//
// Clear is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_TxResult) Clear(fd protoreflect.FieldDescriptor) {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.TxResult.contract_address":
		x.ContractAddress = ""
	case "cosmos.evm.vm.v1.TxResult.bloom":
		x.Bloom = nil
	case "cosmos.evm.vm.v1.TxResult.tx_logs":
		x.TxLogs = nil
	case "cosmos.evm.vm.v1.TxResult.ret":
		x.Ret = nil
	case "cosmos.evm.vm.v1.TxResult.reverted":
		x.Reverted = false
	case "cosmos.evm.vm.v1.TxResult.gas_used":
		x.GasUsed = uint64(0)
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.TxResult"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.TxResult does not contain field %s", fd.FullName()))
	}
}

// Get retrieves the value for a field.
//
// For unpopulated scalars, it returns the default value, where
// the default value of a bytes scalar is guaranteed to be a copy.
// For unpopulated composite types, it returns an empty, read-only view
// of the value; to obtain a mutable reference, use Mutable.
func (x *fastReflection_TxResult) Get(descriptor protoreflect.FieldDescriptor) protoreflect.Value {
	switch descriptor.FullName() {
	case "cosmos.evm.vm.v1.TxResult.contract_address":
		value := x.ContractAddress
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.TxResult.bloom":
		value := x.Bloom
		return protoreflect.ValueOfBytes(value)
	case "cosmos.evm.vm.v1.TxResult.tx_logs":
		value := x.TxLogs
		return protoreflect.ValueOfMessage(value.ProtoReflect())
	case "cosmos.evm.vm.v1.TxResult.ret":
		value := x.Ret
		return protoreflect.ValueOfBytes(value)
	case "cosmos.evm.vm.v1.TxResult.reverted":
		value := x.Reverted
		return protoreflect.ValueOfBool(value)
	case "cosmos.evm.vm.v1.TxResult.gas_used":
		value := x.GasUsed
		return protoreflect.ValueOfUint64(value)
	default:
		if descriptor.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.TxResult"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.TxResult does not contain field %s", descriptor.FullName()))
	}
}

// Set stores the value for a field.
//
// For a field belonging to a oneof, it implicitly clears any other field
// that may be currently set within the same oneof.
// For extension fields, it implicitly stores the provided ExtensionType.
// When setting a composite type, it is unspecified whether the stored value
// aliases the source's memory in any way. If the composite value is an
// empty, read-only value, then it panics.
//
// Set is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_TxResult) Set(fd protoreflect.FieldDescriptor, value protoreflect.Value) {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.TxResult.contract_address":
		x.ContractAddress = value.Interface().(string)
	case "cosmos.evm.vm.v1.TxResult.bloom":
		x.Bloom = value.Bytes()
	case "cosmos.evm.vm.v1.TxResult.tx_logs":
		x.TxLogs = value.Message().Interface().(*TransactionLogs)
	case "cosmos.evm.vm.v1.TxResult.ret":
		x.Ret = value.Bytes()
	case "cosmos.evm.vm.v1.TxResult.reverted":
		x.Reverted = value.Bool()
	case "cosmos.evm.vm.v1.TxResult.gas_used":
		x.GasUsed = value.Uint()
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.TxResult"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.TxResult does not contain field %s", fd.FullName()))
	}
}

// Mutable returns a mutable reference to a composite type.
//
// If the field is unpopulated, it may allocate a composite value.
// For a field belonging to a oneof, it implicitly clears any other field
// that may be currently set within the same oneof.
// For extension fields, it implicitly stores the provided ExtensionType
// if not already stored.
// It panics if the field does not contain a composite type.
//
// Mutable is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_TxResult) Mutable(fd protoreflect.FieldDescriptor) protoreflect.Value {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.TxResult.tx_logs":
		if x.TxLogs == nil {
			x.TxLogs = new(TransactionLogs)
		}
		return protoreflect.ValueOfMessage(x.TxLogs.ProtoReflect())
	case "cosmos.evm.vm.v1.TxResult.contract_address":
		panic(fmt.Errorf("field contract_address of message cosmos.evm.vm.v1.TxResult is not mutable"))
	case "cosmos.evm.vm.v1.TxResult.bloom":
		panic(fmt.Errorf("field bloom of message cosmos.evm.vm.v1.TxResult is not mutable"))
	case "cosmos.evm.vm.v1.TxResult.ret":
		panic(fmt.Errorf("field ret of message cosmos.evm.vm.v1.TxResult is not mutable"))
	case "cosmos.evm.vm.v1.TxResult.reverted":
		panic(fmt.Errorf("field reverted of message cosmos.evm.vm.v1.TxResult is not mutable"))
	case "cosmos.evm.vm.v1.TxResult.gas_used":
		panic(fmt.Errorf("field gas_used of message cosmos.evm.vm.v1.TxResult is not mutable"))
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.TxResult"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.TxResult does not contain field %s", fd.FullName()))
	}
}

// NewField returns a new value that is assignable to the field
// for the given descriptor. For scalars, this returns the default value.
// For lists, maps, and messages, this returns a new, empty, mutable value.
func (x *fastReflection_TxResult) NewField(fd protoreflect.FieldDescriptor) protoreflect.Value {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.TxResult.contract_address":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.TxResult.bloom":
		return protoreflect.ValueOfBytes(nil)
	case "cosmos.evm.vm.v1.TxResult.tx_logs":
		m := new(TransactionLogs)
		return protoreflect.ValueOfMessage(m.ProtoReflect())
	case "cosmos.evm.vm.v1.TxResult.ret":
		return protoreflect.ValueOfBytes(nil)
	case "cosmos.evm.vm.v1.TxResult.reverted":
		return protoreflect.ValueOfBool(false)
	case "cosmos.evm.vm.v1.TxResult.gas_used":
		return protoreflect.ValueOfUint64(uint64(0))
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.TxResult"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.TxResult does not contain field %s", fd.FullName()))
	}
}

// WhichOneof reports which field within the oneof is populated,
// returning nil if none are populated.
// It panics if the oneof descriptor does not belong to this message.
func (x *fastReflection_TxResult) WhichOneof(d protoreflect.OneofDescriptor) protoreflect.FieldDescriptor {
	switch d.FullName() {
	default:
		panic(fmt.Errorf("%s is not a oneof field in cosmos.evm.vm.v1.TxResult", d.FullName()))
	}
	panic("unreachable")
}

// GetUnknown retrieves the entire list of unknown fields.
// The caller may only mutate the contents of the RawFields
// if the mutated bytes are stored back into the message with SetUnknown.
func (x *fastReflection_TxResult) GetUnknown() protoreflect.RawFields {
	return x.unknownFields
}

// SetUnknown stores an entire list of unknown fields.
// The raw fields must be syntactically valid according to the wire format.
// An implementation may panic if this is not the case.
// Once stored, the caller must not mutate the content of the RawFields.
// An empty RawFields may be passed to clear the fields.
//
// SetUnknown is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_TxResult) SetUnknown(fields protoreflect.RawFields) {
	x.unknownFields = fields
}

// IsValid reports whether the message is valid.
//
// An invalid message is an empty, read-only value.
//
// An invalid message often corresponds to a nil pointer of the concrete
// message type, but the details are implementation dependent.
// Validity is not part of the protobuf data model, and may not
// be preserved in marshaling or other operations.
func (x *fastReflection_TxResult) IsValid() bool {
	return x != nil
}

// ProtoMethods returns optional fastReflectionFeature-path implementations of various operations.
// This method may return nil.
//
// The returned methods type is identical to
// "google.golang.org/protobuf/runtime/protoiface".Methods.
// Consult the protoiface package documentation for details.
func (x *fastReflection_TxResult) ProtoMethods() *protoiface.Methods {
	size := func(input protoiface.SizeInput) protoiface.SizeOutput {
		x := input.Message.Interface().(*TxResult)
		if x == nil {
			return protoiface.SizeOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Size:              0,
			}
		}
		options := runtime.SizeInputToOptions(input)
		_ = options
		var n int
		var l int
		_ = l
		l = len(x.ContractAddress)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		l = len(x.Bloom)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		if x.TxLogs != nil {
			l = options.Size(x.TxLogs)
			n += 1 + l + runtime.Sov(uint64(l))
		}
		l = len(x.Ret)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		if x.Reverted {
			n += 2
		}
		if x.GasUsed != 0 {
			n += 1 + runtime.Sov(uint64(x.GasUsed))
		}
		if x.unknownFields != nil {
			n += len(x.unknownFields)
		}
		return protoiface.SizeOutput{
			NoUnkeyedLiterals: input.NoUnkeyedLiterals,
			Size:              n,
		}
	}

	marshal := func(input protoiface.MarshalInput) (protoiface.MarshalOutput, error) {
		x := input.Message.Interface().(*TxResult)
		if x == nil {
			return protoiface.MarshalOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Buf:               input.Buf,
			}, nil
		}
		options := runtime.MarshalInputToOptions(input)
		_ = options
		size := options.Size(x)
		dAtA := make([]byte, size)
		i := len(dAtA)
		_ = i
		var l int
		_ = l
		if x.unknownFields != nil {
			i -= len(x.unknownFields)
			copy(dAtA[i:], x.unknownFields)
		}
		if x.GasUsed != 0 {
			i = runtime.EncodeVarint(dAtA, i, uint64(x.GasUsed))
			i--
			dAtA[i] = 0x30
		}
		if x.Reverted {
			i--
			if x.Reverted {
				dAtA[i] = 1
			} else {
				dAtA[i] = 0
			}
			i--
			dAtA[i] = 0x28
		}
		if len(x.Ret) > 0 {
			i -= len(x.Ret)
			copy(dAtA[i:], x.Ret)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.Ret)))
			i--
			dAtA[i] = 0x22
		}
		if x.TxLogs != nil {
			encoded, err := options.Marshal(x.TxLogs)
			if err != nil {
				return protoiface.MarshalOutput{
					NoUnkeyedLiterals: input.NoUnkeyedLiterals,
					Buf:               input.Buf,
				}, err
			}
			i -= len(encoded)
			copy(dAtA[i:], encoded)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(encoded)))
			i--
			dAtA[i] = 0x1a
		}
		if len(x.Bloom) > 0 {
			i -= len(x.Bloom)
			copy(dAtA[i:], x.Bloom)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.Bloom)))
			i--
			dAtA[i] = 0x12
		}
		if len(x.ContractAddress) > 0 {
			i -= len(x.ContractAddress)
			copy(dAtA[i:], x.ContractAddress)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.ContractAddress)))
			i--
			dAtA[i] = 0xa
		}
		if input.Buf != nil {
			input.Buf = append(input.Buf, dAtA...)
		} else {
			input.Buf = dAtA
		}
		return protoiface.MarshalOutput{
			NoUnkeyedLiterals: input.NoUnkeyedLiterals,
			Buf:               input.Buf,
		}, nil
	}
	unmarshal := func(input protoiface.UnmarshalInput) (protoiface.UnmarshalOutput, error) {
		x := input.Message.Interface().(*TxResult)
		if x == nil {
			return protoiface.UnmarshalOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Flags:             input.Flags,
			}, nil
		}
		options := runtime.UnmarshalInputToOptions(input)
		_ = options
		dAtA := input.Buf
		l := len(dAtA)
		iNdEx := 0
		for iNdEx < l {
			preIndex := iNdEx
			var wire uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
				}
				if iNdEx >= l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				wire |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			fieldNum := int32(wire >> 3)
			wireType := int(wire & 0x7)
			if wireType == 4 {
				return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: TxResult: wiretype end group for non-group")
			}
			if fieldNum <= 0 {
				return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: TxResult: illegal tag %d (wire type %d)", fieldNum, wire)
			}
			switch fieldNum {
			case 1:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field ContractAddress", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.ContractAddress = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 2:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Bloom", wireType)
				}
				var byteLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					byteLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if byteLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + byteLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.Bloom = append(x.Bloom[:0], dAtA[iNdEx:postIndex]...)
				if x.Bloom == nil {
					x.Bloom = []byte{}
				}
				iNdEx = postIndex
			case 3:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field TxLogs", wireType)
				}
				var msglen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					msglen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if msglen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + msglen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				if x.TxLogs == nil {
					x.TxLogs = &TransactionLogs{}
				}
				if err := options.Unmarshal(dAtA[iNdEx:postIndex], x.TxLogs); err != nil {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, err
				}
				iNdEx = postIndex
			case 4:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Ret", wireType)
				}
				var byteLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					byteLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if byteLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + byteLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.Ret = append(x.Ret[:0], dAtA[iNdEx:postIndex]...)
				if x.Ret == nil {
					x.Ret = []byte{}
				}
				iNdEx = postIndex
			case 5:
				if wireType != 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Reverted", wireType)
				}
				var v int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				x.Reverted = bool(v != 0)
			case 6:
				if wireType != 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field GasUsed", wireType)
				}
				x.GasUsed = 0
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					x.GasUsed |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
			default:
				iNdEx = preIndex
				skippy, err := runtime.Skip(dAtA[iNdEx:])
				if err != nil {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, err
				}
				if (skippy < 0) || (iNdEx+skippy) < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if (iNdEx + skippy) > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				if !options.DiscardUnknown {
					x.unknownFields = append(x.unknownFields, dAtA[iNdEx:iNdEx+skippy]...)
				}
				iNdEx += skippy
			}
		}

		if iNdEx > l {
			return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
		}
		return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, nil
	}
	return &protoiface.Methods{
		NoUnkeyedLiterals: struct{}{},
		Flags:             protoiface.SupportMarshalDeterministic | protoiface.SupportUnmarshalDiscardUnknown,
		Size:              size,
		Marshal:           marshal,
		Unmarshal:         unmarshal,
		Merge:             nil,
		CheckInitialized:  nil,
	}
}

var _ protoreflect.List = (*_AccessTuple_2_list)(nil)

type _AccessTuple_2_list struct {
	list *[]string
}

func (x *_AccessTuple_2_list) Len() int {
	if x.list == nil {
		return 0
	}
	return len(*x.list)
}

func (x *_AccessTuple_2_list) Get(i int) protoreflect.Value {
	return protoreflect.ValueOfString((*x.list)[i])
}

func (x *_AccessTuple_2_list) Set(i int, value protoreflect.Value) {
	valueUnwrapped := value.String()
	concreteValue := valueUnwrapped
	(*x.list)[i] = concreteValue
}

func (x *_AccessTuple_2_list) Append(value protoreflect.Value) {
	valueUnwrapped := value.String()
	concreteValue := valueUnwrapped
	*x.list = append(*x.list, concreteValue)
}

func (x *_AccessTuple_2_list) AppendMutable() protoreflect.Value {
	panic(fmt.Errorf("AppendMutable can not be called on message AccessTuple at list field StorageKeys as it is not of Message kind"))
}

func (x *_AccessTuple_2_list) Truncate(n int) {
	*x.list = (*x.list)[:n]
}

func (x *_AccessTuple_2_list) NewElement() protoreflect.Value {
	v := ""
	return protoreflect.ValueOfString(v)
}

func (x *_AccessTuple_2_list) IsValid() bool {
	return x.list != nil
}

var (
	md_AccessTuple              protoreflect.MessageDescriptor
	fd_AccessTuple_address      protoreflect.FieldDescriptor
	fd_AccessTuple_storage_keys protoreflect.FieldDescriptor
)

func init() {
	file_cosmos_evm_vm_v1_evm_proto_init()
	md_AccessTuple = File_cosmos_evm_vm_v1_evm_proto.Messages().ByName("AccessTuple")
	fd_AccessTuple_address = md_AccessTuple.Fields().ByName("address")
	fd_AccessTuple_storage_keys = md_AccessTuple.Fields().ByName("storage_keys")
}

var _ protoreflect.Message = (*fastReflection_AccessTuple)(nil)

type fastReflection_AccessTuple AccessTuple

func (x *AccessTuple) ProtoReflect() protoreflect.Message {
	return (*fastReflection_AccessTuple)(x)
}

func (x *AccessTuple) slowProtoReflect() protoreflect.Message {
	mi := &file_cosmos_evm_vm_v1_evm_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

var _fastReflection_AccessTuple_messageType fastReflection_AccessTuple_messageType
var _ protoreflect.MessageType = fastReflection_AccessTuple_messageType{}

type fastReflection_AccessTuple_messageType struct{}

func (x fastReflection_AccessTuple_messageType) Zero() protoreflect.Message {
	return (*fastReflection_AccessTuple)(nil)
}
func (x fastReflection_AccessTuple_messageType) New() protoreflect.Message {
	return new(fastReflection_AccessTuple)
}
func (x fastReflection_AccessTuple_messageType) Descriptor() protoreflect.MessageDescriptor {
	return md_AccessTuple
}

// Descriptor returns message descriptor, which contains only the protobuf
// type information for the message.
func (x *fastReflection_AccessTuple) Descriptor() protoreflect.MessageDescriptor {
	return md_AccessTuple
}

// Type returns the message type, which encapsulates both Go and protobuf
// type information. If the Go type information is not needed,
// it is recommended that the message descriptor be used instead.
func (x *fastReflection_AccessTuple) Type() protoreflect.MessageType {
	return _fastReflection_AccessTuple_messageType
}

// New returns a newly allocated and mutable empty message.
func (x *fastReflection_AccessTuple) New() protoreflect.Message {
	return new(fastReflection_AccessTuple)
}

// Interface unwraps the message reflection interface and
// returns the underlying ProtoMessage interface.
func (x *fastReflection_AccessTuple) Interface() protoreflect.ProtoMessage {
	return (*AccessTuple)(x)
}

// Range iterates over every populated field in an undefined order,
// calling f for each field descriptor and value encountered.
// Range returns immediately if f returns false.
// While iterating, mutating operations may only be performed
// on the current field descriptor.
func (x *fastReflection_AccessTuple) Range(f func(protoreflect.FieldDescriptor, protoreflect.Value) bool) {
	if x.Address != "" {
		value := protoreflect.ValueOfString(x.Address)
		if !f(fd_AccessTuple_address, value) {
			return
		}
	}
	if len(x.StorageKeys) != 0 {
		value := protoreflect.ValueOfList(&_AccessTuple_2_list{list: &x.StorageKeys})
		if !f(fd_AccessTuple_storage_keys, value) {
			return
		}
	}
}

// Has reports whether a field is populated.
//
// Some fields have the property of nullability where it is possible to
// distinguish between the default value of a field and whether the field
// was explicitly populated with the default value. Singular message fields,
// member fields of a oneof, and proto2 scalar fields are nullable. Such
// fields are populated only if explicitly set.
//
// In other cases (aside from the nullable cases above),
// a proto3 scalar field is populated if it contains a non-zero value, and
// a repeated field is populated if it is non-empty.
func (x *fastReflection_AccessTuple) Has(fd protoreflect.FieldDescriptor) bool {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.AccessTuple.address":
		return x.Address != ""
	case "cosmos.evm.vm.v1.AccessTuple.storage_keys":
		return len(x.StorageKeys) != 0
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.AccessTuple"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.AccessTuple does not contain field %s", fd.FullName()))
	}
}

// Clear clears the field such that a subsequent Has call reports false.
//
// Clearing an extension field clears both the extension type and value
// associated with the given field number.
//
// Clear is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_AccessTuple) Clear(fd protoreflect.FieldDescriptor) {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.AccessTuple.address":
		x.Address = ""
	case "cosmos.evm.vm.v1.AccessTuple.storage_keys":
		x.StorageKeys = nil
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.AccessTuple"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.AccessTuple does not contain field %s", fd.FullName()))
	}
}

// Get retrieves the value for a field.
//
// For unpopulated scalars, it returns the default value, where
// the default value of a bytes scalar is guaranteed to be a copy.
// For unpopulated composite types, it returns an empty, read-only view
// of the value; to obtain a mutable reference, use Mutable.
func (x *fastReflection_AccessTuple) Get(descriptor protoreflect.FieldDescriptor) protoreflect.Value {
	switch descriptor.FullName() {
	case "cosmos.evm.vm.v1.AccessTuple.address":
		value := x.Address
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.AccessTuple.storage_keys":
		if len(x.StorageKeys) == 0 {
			return protoreflect.ValueOfList(&_AccessTuple_2_list{})
		}
		listValue := &_AccessTuple_2_list{list: &x.StorageKeys}
		return protoreflect.ValueOfList(listValue)
	default:
		if descriptor.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.AccessTuple"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.AccessTuple does not contain field %s", descriptor.FullName()))
	}
}

// Set stores the value for a field.
//
// For a field belonging to a oneof, it implicitly clears any other field
// that may be currently set within the same oneof.
// For extension fields, it implicitly stores the provided ExtensionType.
// When setting a composite type, it is unspecified whether the stored value
// aliases the source's memory in any way. If the composite value is an
// empty, read-only value, then it panics.
//
// Set is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_AccessTuple) Set(fd protoreflect.FieldDescriptor, value protoreflect.Value) {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.AccessTuple.address":
		x.Address = value.Interface().(string)
	case "cosmos.evm.vm.v1.AccessTuple.storage_keys":
		lv := value.List()
		clv := lv.(*_AccessTuple_2_list)
		x.StorageKeys = *clv.list
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.AccessTuple"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.AccessTuple does not contain field %s", fd.FullName()))
	}
}

// Mutable returns a mutable reference to a composite type.
//
// If the field is unpopulated, it may allocate a composite value.
// For a field belonging to a oneof, it implicitly clears any other field
// that may be currently set within the same oneof.
// For extension fields, it implicitly stores the provided ExtensionType
// if not already stored.
// It panics if the field does not contain a composite type.
//
// Mutable is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_AccessTuple) Mutable(fd protoreflect.FieldDescriptor) protoreflect.Value {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.AccessTuple.storage_keys":
		if x.StorageKeys == nil {
			x.StorageKeys = []string{}
		}
		value := &_AccessTuple_2_list{list: &x.StorageKeys}
		return protoreflect.ValueOfList(value)
	case "cosmos.evm.vm.v1.AccessTuple.address":
		panic(fmt.Errorf("field address of message cosmos.evm.vm.v1.AccessTuple is not mutable"))
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.AccessTuple"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.AccessTuple does not contain field %s", fd.FullName()))
	}
}

// NewField returns a new value that is assignable to the field
// for the given descriptor. For scalars, this returns the default value.
// For lists, maps, and messages, this returns a new, empty, mutable value.
func (x *fastReflection_AccessTuple) NewField(fd protoreflect.FieldDescriptor) protoreflect.Value {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.AccessTuple.address":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.AccessTuple.storage_keys":
		list := []string{}
		return protoreflect.ValueOfList(&_AccessTuple_2_list{list: &list})
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.AccessTuple"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.AccessTuple does not contain field %s", fd.FullName()))
	}
}

// WhichOneof reports which field within the oneof is populated,
// returning nil if none are populated.
// It panics if the oneof descriptor does not belong to this message.
func (x *fastReflection_AccessTuple) WhichOneof(d protoreflect.OneofDescriptor) protoreflect.FieldDescriptor {
	switch d.FullName() {
	default:
		panic(fmt.Errorf("%s is not a oneof field in cosmos.evm.vm.v1.AccessTuple", d.FullName()))
	}
	panic("unreachable")
}

// GetUnknown retrieves the entire list of unknown fields.
// The caller may only mutate the contents of the RawFields
// if the mutated bytes are stored back into the message with SetUnknown.
func (x *fastReflection_AccessTuple) GetUnknown() protoreflect.RawFields {
	return x.unknownFields
}

// SetUnknown stores an entire list of unknown fields.
// The raw fields must be syntactically valid according to the wire format.
// An implementation may panic if this is not the case.
// Once stored, the caller must not mutate the content of the RawFields.
// An empty RawFields may be passed to clear the fields.
//
// SetUnknown is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_AccessTuple) SetUnknown(fields protoreflect.RawFields) {
	x.unknownFields = fields
}

// IsValid reports whether the message is valid.
//
// An invalid message is an empty, read-only value.
//
// An invalid message often corresponds to a nil pointer of the concrete
// message type, but the details are implementation dependent.
// Validity is not part of the protobuf data model, and may not
// be preserved in marshaling or other operations.
func (x *fastReflection_AccessTuple) IsValid() bool {
	return x != nil
}

// ProtoMethods returns optional fastReflectionFeature-path implementations of various operations.
// This method may return nil.
//
// The returned methods type is identical to
// "google.golang.org/protobuf/runtime/protoiface".Methods.
// Consult the protoiface package documentation for details.
func (x *fastReflection_AccessTuple) ProtoMethods() *protoiface.Methods {
	size := func(input protoiface.SizeInput) protoiface.SizeOutput {
		x := input.Message.Interface().(*AccessTuple)
		if x == nil {
			return protoiface.SizeOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Size:              0,
			}
		}
		options := runtime.SizeInputToOptions(input)
		_ = options
		var n int
		var l int
		_ = l
		l = len(x.Address)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		if len(x.StorageKeys) > 0 {
			for _, s := range x.StorageKeys {
				l = len(s)
				n += 1 + l + runtime.Sov(uint64(l))
			}
		}
		if x.unknownFields != nil {
			n += len(x.unknownFields)
		}
		return protoiface.SizeOutput{
			NoUnkeyedLiterals: input.NoUnkeyedLiterals,
			Size:              n,
		}
	}

	marshal := func(input protoiface.MarshalInput) (protoiface.MarshalOutput, error) {
		x := input.Message.Interface().(*AccessTuple)
		if x == nil {
			return protoiface.MarshalOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Buf:               input.Buf,
			}, nil
		}
		options := runtime.MarshalInputToOptions(input)
		_ = options
		size := options.Size(x)
		dAtA := make([]byte, size)
		i := len(dAtA)
		_ = i
		var l int
		_ = l
		if x.unknownFields != nil {
			i -= len(x.unknownFields)
			copy(dAtA[i:], x.unknownFields)
		}
		if len(x.StorageKeys) > 0 {
			for iNdEx := len(x.StorageKeys) - 1; iNdEx >= 0; iNdEx-- {
				i -= len(x.StorageKeys[iNdEx])
				copy(dAtA[i:], x.StorageKeys[iNdEx])
				i = runtime.EncodeVarint(dAtA, i, uint64(len(x.StorageKeys[iNdEx])))
				i--
				dAtA[i] = 0x12
			}
		}
		if len(x.Address) > 0 {
			i -= len(x.Address)
			copy(dAtA[i:], x.Address)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.Address)))
			i--
			dAtA[i] = 0xa
		}
		if input.Buf != nil {
			input.Buf = append(input.Buf, dAtA...)
		} else {
			input.Buf = dAtA
		}
		return protoiface.MarshalOutput{
			NoUnkeyedLiterals: input.NoUnkeyedLiterals,
			Buf:               input.Buf,
		}, nil
	}
	unmarshal := func(input protoiface.UnmarshalInput) (protoiface.UnmarshalOutput, error) {
		x := input.Message.Interface().(*AccessTuple)
		if x == nil {
			return protoiface.UnmarshalOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Flags:             input.Flags,
			}, nil
		}
		options := runtime.UnmarshalInputToOptions(input)
		_ = options
		dAtA := input.Buf
		l := len(dAtA)
		iNdEx := 0
		for iNdEx < l {
			preIndex := iNdEx
			var wire uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
				}
				if iNdEx >= l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				wire |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			fieldNum := int32(wire >> 3)
			wireType := int(wire & 0x7)
			if wireType == 4 {
				return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: AccessTuple: wiretype end group for non-group")
			}
			if fieldNum <= 0 {
				return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: AccessTuple: illegal tag %d (wire type %d)", fieldNum, wire)
			}
			switch fieldNum {
			case 1:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Address", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.Address = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 2:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field StorageKeys", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.StorageKeys = append(x.StorageKeys, string(dAtA[iNdEx:postIndex]))
				iNdEx = postIndex
			default:
				iNdEx = preIndex
				skippy, err := runtime.Skip(dAtA[iNdEx:])
				if err != nil {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, err
				}
				if (skippy < 0) || (iNdEx+skippy) < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if (iNdEx + skippy) > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				if !options.DiscardUnknown {
					x.unknownFields = append(x.unknownFields, dAtA[iNdEx:iNdEx+skippy]...)
				}
				iNdEx += skippy
			}
		}

		if iNdEx > l {
			return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
		}
		return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, nil
	}
	return &protoiface.Methods{
		NoUnkeyedLiterals: struct{}{},
		Flags:             protoiface.SupportMarshalDeterministic | protoiface.SupportUnmarshalDiscardUnknown,
		Size:              size,
		Marshal:           marshal,
		Unmarshal:         unmarshal,
		Merge:             nil,
		CheckInitialized:  nil,
	}
}

var (
	md_TraceConfig                    protoreflect.MessageDescriptor
	fd_TraceConfig_tracer             protoreflect.FieldDescriptor
	fd_TraceConfig_timeout            protoreflect.FieldDescriptor
	fd_TraceConfig_reexec             protoreflect.FieldDescriptor
	fd_TraceConfig_disable_stack      protoreflect.FieldDescriptor
	fd_TraceConfig_disable_storage    protoreflect.FieldDescriptor
	fd_TraceConfig_debug              protoreflect.FieldDescriptor
	fd_TraceConfig_limit              protoreflect.FieldDescriptor
	fd_TraceConfig_overrides          protoreflect.FieldDescriptor
	fd_TraceConfig_enable_memory      protoreflect.FieldDescriptor
	fd_TraceConfig_enable_return_data protoreflect.FieldDescriptor
	fd_TraceConfig_tracer_json_config protoreflect.FieldDescriptor
)

func init() {
	file_cosmos_evm_vm_v1_evm_proto_init()
	md_TraceConfig = File_cosmos_evm_vm_v1_evm_proto.Messages().ByName("TraceConfig")
	fd_TraceConfig_tracer = md_TraceConfig.Fields().ByName("tracer")
	fd_TraceConfig_timeout = md_TraceConfig.Fields().ByName("timeout")
	fd_TraceConfig_reexec = md_TraceConfig.Fields().ByName("reexec")
	fd_TraceConfig_disable_stack = md_TraceConfig.Fields().ByName("disable_stack")
	fd_TraceConfig_disable_storage = md_TraceConfig.Fields().ByName("disable_storage")
	fd_TraceConfig_debug = md_TraceConfig.Fields().ByName("debug")
	fd_TraceConfig_limit = md_TraceConfig.Fields().ByName("limit")
	fd_TraceConfig_overrides = md_TraceConfig.Fields().ByName("overrides")
	fd_TraceConfig_enable_memory = md_TraceConfig.Fields().ByName("enable_memory")
	fd_TraceConfig_enable_return_data = md_TraceConfig.Fields().ByName("enable_return_data")
	fd_TraceConfig_tracer_json_config = md_TraceConfig.Fields().ByName("tracer_json_config")
}

var _ protoreflect.Message = (*fastReflection_TraceConfig)(nil)

type fastReflection_TraceConfig TraceConfig

func (x *TraceConfig) ProtoReflect() protoreflect.Message {
	return (*fastReflection_TraceConfig)(x)
}

func (x *TraceConfig) slowProtoReflect() protoreflect.Message {
	mi := &file_cosmos_evm_vm_v1_evm_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

var _fastReflection_TraceConfig_messageType fastReflection_TraceConfig_messageType
var _ protoreflect.MessageType = fastReflection_TraceConfig_messageType{}

type fastReflection_TraceConfig_messageType struct{}

func (x fastReflection_TraceConfig_messageType) Zero() protoreflect.Message {
	return (*fastReflection_TraceConfig)(nil)
}
func (x fastReflection_TraceConfig_messageType) New() protoreflect.Message {
	return new(fastReflection_TraceConfig)
}
func (x fastReflection_TraceConfig_messageType) Descriptor() protoreflect.MessageDescriptor {
	return md_TraceConfig
}

// Descriptor returns message descriptor, which contains only the protobuf
// type information for the message.
func (x *fastReflection_TraceConfig) Descriptor() protoreflect.MessageDescriptor {
	return md_TraceConfig
}

// Type returns the message type, which encapsulates both Go and protobuf
// type information. If the Go type information is not needed,
// it is recommended that the message descriptor be used instead.
func (x *fastReflection_TraceConfig) Type() protoreflect.MessageType {
	return _fastReflection_TraceConfig_messageType
}

// New returns a newly allocated and mutable empty message.
func (x *fastReflection_TraceConfig) New() protoreflect.Message {
	return new(fastReflection_TraceConfig)
}

// Interface unwraps the message reflection interface and
// returns the underlying ProtoMessage interface.
func (x *fastReflection_TraceConfig) Interface() protoreflect.ProtoMessage {
	return (*TraceConfig)(x)
}

// Range iterates over every populated field in an undefined order,
// calling f for each field descriptor and value encountered.
// Range returns immediately if f returns false.
// While iterating, mutating operations may only be performed
// on the current field descriptor.
func (x *fastReflection_TraceConfig) Range(f func(protoreflect.FieldDescriptor, protoreflect.Value) bool) {
	if x.Tracer != "" {
		value := protoreflect.ValueOfString(x.Tracer)
		if !f(fd_TraceConfig_tracer, value) {
			return
		}
	}
	if x.Timeout != "" {
		value := protoreflect.ValueOfString(x.Timeout)
		if !f(fd_TraceConfig_timeout, value) {
			return
		}
	}
	if x.Reexec != uint64(0) {
		value := protoreflect.ValueOfUint64(x.Reexec)
		if !f(fd_TraceConfig_reexec, value) {
			return
		}
	}
	if x.DisableStack != false {
		value := protoreflect.ValueOfBool(x.DisableStack)
		if !f(fd_TraceConfig_disable_stack, value) {
			return
		}
	}
	if x.DisableStorage != false {
		value := protoreflect.ValueOfBool(x.DisableStorage)
		if !f(fd_TraceConfig_disable_storage, value) {
			return
		}
	}
	if x.Debug != false {
		value := protoreflect.ValueOfBool(x.Debug)
		if !f(fd_TraceConfig_debug, value) {
			return
		}
	}
	if x.Limit != int32(0) {
		value := protoreflect.ValueOfInt32(x.Limit)
		if !f(fd_TraceConfig_limit, value) {
			return
		}
	}
	if x.Overrides != nil {
		value := protoreflect.ValueOfMessage(x.Overrides.ProtoReflect())
		if !f(fd_TraceConfig_overrides, value) {
			return
		}
	}
	if x.EnableMemory != false {
		value := protoreflect.ValueOfBool(x.EnableMemory)
		if !f(fd_TraceConfig_enable_memory, value) {
			return
		}
	}
	if x.EnableReturnData != false {
		value := protoreflect.ValueOfBool(x.EnableReturnData)
		if !f(fd_TraceConfig_enable_return_data, value) {
			return
		}
	}
	if x.TracerJsonConfig != "" {
		value := protoreflect.ValueOfString(x.TracerJsonConfig)
		if !f(fd_TraceConfig_tracer_json_config, value) {
			return
		}
	}
}

// Has reports whether a field is populated.
//
// Some fields have the property of nullability where it is possible to
// distinguish between the default value of a field and whether the field
// was explicitly populated with the default value. Singular message fields,
// member fields of a oneof, and proto2 scalar fields are nullable. Such
// fields are populated only if explicitly set.
//
// In other cases (aside from the nullable cases above),
// a proto3 scalar field is populated if it contains a non-zero value, and
// a repeated field is populated if it is non-empty.
func (x *fastReflection_TraceConfig) Has(fd protoreflect.FieldDescriptor) bool {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.TraceConfig.tracer":
		return x.Tracer != ""
	case "cosmos.evm.vm.v1.TraceConfig.timeout":
		return x.Timeout != ""
	case "cosmos.evm.vm.v1.TraceConfig.reexec":
		return x.Reexec != uint64(0)
	case "cosmos.evm.vm.v1.TraceConfig.disable_stack":
		return x.DisableStack != false
	case "cosmos.evm.vm.v1.TraceConfig.disable_storage":
		return x.DisableStorage != false
	case "cosmos.evm.vm.v1.TraceConfig.debug":
		return x.Debug != false
	case "cosmos.evm.vm.v1.TraceConfig.limit":
		return x.Limit != int32(0)
	case "cosmos.evm.vm.v1.TraceConfig.overrides":
		return x.Overrides != nil
	case "cosmos.evm.vm.v1.TraceConfig.enable_memory":
		return x.EnableMemory != false
	case "cosmos.evm.vm.v1.TraceConfig.enable_return_data":
		return x.EnableReturnData != false
	case "cosmos.evm.vm.v1.TraceConfig.tracer_json_config":
		return x.TracerJsonConfig != ""
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.TraceConfig"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.TraceConfig does not contain field %s", fd.FullName()))
	}
}

// Clear clears the field such that a subsequent Has call reports false.
//
// Clearing an extension field clears both the extension type and value
// associated with the given field number.
//
// Clear is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_TraceConfig) Clear(fd protoreflect.FieldDescriptor) {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.TraceConfig.tracer":
		x.Tracer = ""
	case "cosmos.evm.vm.v1.TraceConfig.timeout":
		x.Timeout = ""
	case "cosmos.evm.vm.v1.TraceConfig.reexec":
		x.Reexec = uint64(0)
	case "cosmos.evm.vm.v1.TraceConfig.disable_stack":
		x.DisableStack = false
	case "cosmos.evm.vm.v1.TraceConfig.disable_storage":
		x.DisableStorage = false
	case "cosmos.evm.vm.v1.TraceConfig.debug":
		x.Debug = false
	case "cosmos.evm.vm.v1.TraceConfig.limit":
		x.Limit = int32(0)
	case "cosmos.evm.vm.v1.TraceConfig.overrides":
		x.Overrides = nil
	case "cosmos.evm.vm.v1.TraceConfig.enable_memory":
		x.EnableMemory = false
	case "cosmos.evm.vm.v1.TraceConfig.enable_return_data":
		x.EnableReturnData = false
	case "cosmos.evm.vm.v1.TraceConfig.tracer_json_config":
		x.TracerJsonConfig = ""
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.TraceConfig"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.TraceConfig does not contain field %s", fd.FullName()))
	}
}

// Get retrieves the value for a field.
//
// For unpopulated scalars, it returns the default value, where
// the default value of a bytes scalar is guaranteed to be a copy.
// For unpopulated composite types, it returns an empty, read-only view
// of the value; to obtain a mutable reference, use Mutable.
func (x *fastReflection_TraceConfig) Get(descriptor protoreflect.FieldDescriptor) protoreflect.Value {
	switch descriptor.FullName() {
	case "cosmos.evm.vm.v1.TraceConfig.tracer":
		value := x.Tracer
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.TraceConfig.timeout":
		value := x.Timeout
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.TraceConfig.reexec":
		value := x.Reexec
		return protoreflect.ValueOfUint64(value)
	case "cosmos.evm.vm.v1.TraceConfig.disable_stack":
		value := x.DisableStack
		return protoreflect.ValueOfBool(value)
	case "cosmos.evm.vm.v1.TraceConfig.disable_storage":
		value := x.DisableStorage
		return protoreflect.ValueOfBool(value)
	case "cosmos.evm.vm.v1.TraceConfig.debug":
		value := x.Debug
		return protoreflect.ValueOfBool(value)
	case "cosmos.evm.vm.v1.TraceConfig.limit":
		value := x.Limit
		return protoreflect.ValueOfInt32(value)
	case "cosmos.evm.vm.v1.TraceConfig.overrides":
		value := x.Overrides
		return protoreflect.ValueOfMessage(value.ProtoReflect())
	case "cosmos.evm.vm.v1.TraceConfig.enable_memory":
		value := x.EnableMemory
		return protoreflect.ValueOfBool(value)
	case "cosmos.evm.vm.v1.TraceConfig.enable_return_data":
		value := x.EnableReturnData
		return protoreflect.ValueOfBool(value)
	case "cosmos.evm.vm.v1.TraceConfig.tracer_json_config":
		value := x.TracerJsonConfig
		return protoreflect.ValueOfString(value)
	default:
		if descriptor.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.TraceConfig"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.TraceConfig does not contain field %s", descriptor.FullName()))
	}
}

// Set stores the value for a field.
//
// For a field belonging to a oneof, it implicitly clears any other field
// that may be currently set within the same oneof.
// For extension fields, it implicitly stores the provided ExtensionType.
// When setting a composite type, it is unspecified whether the stored value
// aliases the source's memory in any way. If the composite value is an
// empty, read-only value, then it panics.
//
// Set is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_TraceConfig) Set(fd protoreflect.FieldDescriptor, value protoreflect.Value) {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.TraceConfig.tracer":
		x.Tracer = value.Interface().(string)
	case "cosmos.evm.vm.v1.TraceConfig.timeout":
		x.Timeout = value.Interface().(string)
	case "cosmos.evm.vm.v1.TraceConfig.reexec":
		x.Reexec = value.Uint()
	case "cosmos.evm.vm.v1.TraceConfig.disable_stack":
		x.DisableStack = value.Bool()
	case "cosmos.evm.vm.v1.TraceConfig.disable_storage":
		x.DisableStorage = value.Bool()
	case "cosmos.evm.vm.v1.TraceConfig.debug":
		x.Debug = value.Bool()
	case "cosmos.evm.vm.v1.TraceConfig.limit":
		x.Limit = int32(value.Int())
	case "cosmos.evm.vm.v1.TraceConfig.overrides":
		x.Overrides = value.Message().Interface().(*ChainConfig)
	case "cosmos.evm.vm.v1.TraceConfig.enable_memory":
		x.EnableMemory = value.Bool()
	case "cosmos.evm.vm.v1.TraceConfig.enable_return_data":
		x.EnableReturnData = value.Bool()
	case "cosmos.evm.vm.v1.TraceConfig.tracer_json_config":
		x.TracerJsonConfig = value.Interface().(string)
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.TraceConfig"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.TraceConfig does not contain field %s", fd.FullName()))
	}
}

// Mutable returns a mutable reference to a composite type.
//
// If the field is unpopulated, it may allocate a composite value.
// For a field belonging to a oneof, it implicitly clears any other field
// that may be currently set within the same oneof.
// For extension fields, it implicitly stores the provided ExtensionType
// if not already stored.
// It panics if the field does not contain a composite type.
//
// Mutable is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_TraceConfig) Mutable(fd protoreflect.FieldDescriptor) protoreflect.Value {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.TraceConfig.overrides":
		if x.Overrides == nil {
			x.Overrides = new(ChainConfig)
		}
		return protoreflect.ValueOfMessage(x.Overrides.ProtoReflect())
	case "cosmos.evm.vm.v1.TraceConfig.tracer":
		panic(fmt.Errorf("field tracer of message cosmos.evm.vm.v1.TraceConfig is not mutable"))
	case "cosmos.evm.vm.v1.TraceConfig.timeout":
		panic(fmt.Errorf("field timeout of message cosmos.evm.vm.v1.TraceConfig is not mutable"))
	case "cosmos.evm.vm.v1.TraceConfig.reexec":
		panic(fmt.Errorf("field reexec of message cosmos.evm.vm.v1.TraceConfig is not mutable"))
	case "cosmos.evm.vm.v1.TraceConfig.disable_stack":
		panic(fmt.Errorf("field disable_stack of message cosmos.evm.vm.v1.TraceConfig is not mutable"))
	case "cosmos.evm.vm.v1.TraceConfig.disable_storage":
		panic(fmt.Errorf("field disable_storage of message cosmos.evm.vm.v1.TraceConfig is not mutable"))
	case "cosmos.evm.vm.v1.TraceConfig.debug":
		panic(fmt.Errorf("field debug of message cosmos.evm.vm.v1.TraceConfig is not mutable"))
	case "cosmos.evm.vm.v1.TraceConfig.limit":
		panic(fmt.Errorf("field limit of message cosmos.evm.vm.v1.TraceConfig is not mutable"))
	case "cosmos.evm.vm.v1.TraceConfig.enable_memory":
		panic(fmt.Errorf("field enable_memory of message cosmos.evm.vm.v1.TraceConfig is not mutable"))
	case "cosmos.evm.vm.v1.TraceConfig.enable_return_data":
		panic(fmt.Errorf("field enable_return_data of message cosmos.evm.vm.v1.TraceConfig is not mutable"))
	case "cosmos.evm.vm.v1.TraceConfig.tracer_json_config":
		panic(fmt.Errorf("field tracer_json_config of message cosmos.evm.vm.v1.TraceConfig is not mutable"))
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.TraceConfig"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.TraceConfig does not contain field %s", fd.FullName()))
	}
}

// NewField returns a new value that is assignable to the field
// for the given descriptor. For scalars, this returns the default value.
// For lists, maps, and messages, this returns a new, empty, mutable value.
func (x *fastReflection_TraceConfig) NewField(fd protoreflect.FieldDescriptor) protoreflect.Value {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.TraceConfig.tracer":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.TraceConfig.timeout":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.TraceConfig.reexec":
		return protoreflect.ValueOfUint64(uint64(0))
	case "cosmos.evm.vm.v1.TraceConfig.disable_stack":
		return protoreflect.ValueOfBool(false)
	case "cosmos.evm.vm.v1.TraceConfig.disable_storage":
		return protoreflect.ValueOfBool(false)
	case "cosmos.evm.vm.v1.TraceConfig.debug":
		return protoreflect.ValueOfBool(false)
	case "cosmos.evm.vm.v1.TraceConfig.limit":
		return protoreflect.ValueOfInt32(int32(0))
	case "cosmos.evm.vm.v1.TraceConfig.overrides":
		m := new(ChainConfig)
		return protoreflect.ValueOfMessage(m.ProtoReflect())
	case "cosmos.evm.vm.v1.TraceConfig.enable_memory":
		return protoreflect.ValueOfBool(false)
	case "cosmos.evm.vm.v1.TraceConfig.enable_return_data":
		return protoreflect.ValueOfBool(false)
	case "cosmos.evm.vm.v1.TraceConfig.tracer_json_config":
		return protoreflect.ValueOfString("")
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.TraceConfig"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.TraceConfig does not contain field %s", fd.FullName()))
	}
}

// WhichOneof reports which field within the oneof is populated,
// returning nil if none are populated.
// It panics if the oneof descriptor does not belong to this message.
func (x *fastReflection_TraceConfig) WhichOneof(d protoreflect.OneofDescriptor) protoreflect.FieldDescriptor {
	switch d.FullName() {
	default:
		panic(fmt.Errorf("%s is not a oneof field in cosmos.evm.vm.v1.TraceConfig", d.FullName()))
	}
	panic("unreachable")
}

// GetUnknown retrieves the entire list of unknown fields.
// The caller may only mutate the contents of the RawFields
// if the mutated bytes are stored back into the message with SetUnknown.
func (x *fastReflection_TraceConfig) GetUnknown() protoreflect.RawFields {
	return x.unknownFields
}

// SetUnknown stores an entire list of unknown fields.
// The raw fields must be syntactically valid according to the wire format.
// An implementation may panic if this is not the case.
// Once stored, the caller must not mutate the content of the RawFields.
// An empty RawFields may be passed to clear the fields.
//
// SetUnknown is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_TraceConfig) SetUnknown(fields protoreflect.RawFields) {
	x.unknownFields = fields
}

// IsValid reports whether the message is valid.
//
// An invalid message is an empty, read-only value.
//
// An invalid message often corresponds to a nil pointer of the concrete
// message type, but the details are implementation dependent.
// Validity is not part of the protobuf data model, and may not
// be preserved in marshaling or other operations.
func (x *fastReflection_TraceConfig) IsValid() bool {
	return x != nil
}

// ProtoMethods returns optional fastReflectionFeature-path implementations of various operations.
// This method may return nil.
//
// The returned methods type is identical to
// "google.golang.org/protobuf/runtime/protoiface".Methods.
// Consult the protoiface package documentation for details.
func (x *fastReflection_TraceConfig) ProtoMethods() *protoiface.Methods {
	size := func(input protoiface.SizeInput) protoiface.SizeOutput {
		x := input.Message.Interface().(*TraceConfig)
		if x == nil {
			return protoiface.SizeOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Size:              0,
			}
		}
		options := runtime.SizeInputToOptions(input)
		_ = options
		var n int
		var l int
		_ = l
		l = len(x.Tracer)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		l = len(x.Timeout)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		if x.Reexec != 0 {
			n += 1 + runtime.Sov(uint64(x.Reexec))
		}
		if x.DisableStack {
			n += 2
		}
		if x.DisableStorage {
			n += 2
		}
		if x.Debug {
			n += 2
		}
		if x.Limit != 0 {
			n += 1 + runtime.Sov(uint64(x.Limit))
		}
		if x.Overrides != nil {
			l = options.Size(x.Overrides)
			n += 1 + l + runtime.Sov(uint64(l))
		}
		if x.EnableMemory {
			n += 2
		}
		if x.EnableReturnData {
			n += 2
		}
		l = len(x.TracerJsonConfig)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		if x.unknownFields != nil {
			n += len(x.unknownFields)
		}
		return protoiface.SizeOutput{
			NoUnkeyedLiterals: input.NoUnkeyedLiterals,
			Size:              n,
		}
	}

	marshal := func(input protoiface.MarshalInput) (protoiface.MarshalOutput, error) {
		x := input.Message.Interface().(*TraceConfig)
		if x == nil {
			return protoiface.MarshalOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Buf:               input.Buf,
			}, nil
		}
		options := runtime.MarshalInputToOptions(input)
		_ = options
		size := options.Size(x)
		dAtA := make([]byte, size)
		i := len(dAtA)
		_ = i
		var l int
		_ = l
		if x.unknownFields != nil {
			i -= len(x.unknownFields)
			copy(dAtA[i:], x.unknownFields)
		}
		if len(x.TracerJsonConfig) > 0 {
			i -= len(x.TracerJsonConfig)
			copy(dAtA[i:], x.TracerJsonConfig)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.TracerJsonConfig)))
			i--
			dAtA[i] = 0x6a
		}
		if x.EnableReturnData {
			i--
			if x.EnableReturnData {
				dAtA[i] = 1
			} else {
				dAtA[i] = 0
			}
			i--
			dAtA[i] = 0x60
		}
		if x.EnableMemory {
			i--
			if x.EnableMemory {
				dAtA[i] = 1
			} else {
				dAtA[i] = 0
			}
			i--
			dAtA[i] = 0x58
		}
		if x.Overrides != nil {
			encoded, err := options.Marshal(x.Overrides)
			if err != nil {
				return protoiface.MarshalOutput{
					NoUnkeyedLiterals: input.NoUnkeyedLiterals,
					Buf:               input.Buf,
				}, err
			}
			i -= len(encoded)
			copy(dAtA[i:], encoded)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(encoded)))
			i--
			dAtA[i] = 0x52
		}
		if x.Limit != 0 {
			i = runtime.EncodeVarint(dAtA, i, uint64(x.Limit))
			i--
			dAtA[i] = 0x48
		}
		if x.Debug {
			i--
			if x.Debug {
				dAtA[i] = 1
			} else {
				dAtA[i] = 0
			}
			i--
			dAtA[i] = 0x40
		}
		if x.DisableStorage {
			i--
			if x.DisableStorage {
				dAtA[i] = 1
			} else {
				dAtA[i] = 0
			}
			i--
			dAtA[i] = 0x30
		}
		if x.DisableStack {
			i--
			if x.DisableStack {
				dAtA[i] = 1
			} else {
				dAtA[i] = 0
			}
			i--
			dAtA[i] = 0x28
		}
		if x.Reexec != 0 {
			i = runtime.EncodeVarint(dAtA, i, uint64(x.Reexec))
			i--
			dAtA[i] = 0x18
		}
		if len(x.Timeout) > 0 {
			i -= len(x.Timeout)
			copy(dAtA[i:], x.Timeout)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.Timeout)))
			i--
			dAtA[i] = 0x12
		}
		if len(x.Tracer) > 0 {
			i -= len(x.Tracer)
			copy(dAtA[i:], x.Tracer)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.Tracer)))
			i--
			dAtA[i] = 0xa
		}
		if input.Buf != nil {
			input.Buf = append(input.Buf, dAtA...)
		} else {
			input.Buf = dAtA
		}
		return protoiface.MarshalOutput{
			NoUnkeyedLiterals: input.NoUnkeyedLiterals,
			Buf:               input.Buf,
		}, nil
	}
	unmarshal := func(input protoiface.UnmarshalInput) (protoiface.UnmarshalOutput, error) {
		x := input.Message.Interface().(*TraceConfig)
		if x == nil {
			return protoiface.UnmarshalOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Flags:             input.Flags,
			}, nil
		}
		options := runtime.UnmarshalInputToOptions(input)
		_ = options
		dAtA := input.Buf
		l := len(dAtA)
		iNdEx := 0
		for iNdEx < l {
			preIndex := iNdEx
			var wire uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
				}
				if iNdEx >= l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				wire |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			fieldNum := int32(wire >> 3)
			wireType := int(wire & 0x7)
			if wireType == 4 {
				return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: TraceConfig: wiretype end group for non-group")
			}
			if fieldNum <= 0 {
				return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: TraceConfig: illegal tag %d (wire type %d)", fieldNum, wire)
			}
			switch fieldNum {
			case 1:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Tracer", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.Tracer = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 2:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Timeout", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.Timeout = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 3:
				if wireType != 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Reexec", wireType)
				}
				x.Reexec = 0
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					x.Reexec |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
			case 5:
				if wireType != 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field DisableStack", wireType)
				}
				var v int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				x.DisableStack = bool(v != 0)
			case 6:
				if wireType != 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field DisableStorage", wireType)
				}
				var v int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				x.DisableStorage = bool(v != 0)
			case 8:
				if wireType != 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Debug", wireType)
				}
				var v int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				x.Debug = bool(v != 0)
			case 9:
				if wireType != 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Limit", wireType)
				}
				x.Limit = 0
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					x.Limit |= int32(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
			case 10:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Overrides", wireType)
				}
				var msglen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					msglen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if msglen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + msglen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				if x.Overrides == nil {
					x.Overrides = &ChainConfig{}
				}
				if err := options.Unmarshal(dAtA[iNdEx:postIndex], x.Overrides); err != nil {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, err
				}
				iNdEx = postIndex
			case 11:
				if wireType != 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field EnableMemory", wireType)
				}
				var v int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				x.EnableMemory = bool(v != 0)
			case 12:
				if wireType != 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field EnableReturnData", wireType)
				}
				var v int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				x.EnableReturnData = bool(v != 0)
			case 13:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field TracerJsonConfig", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.TracerJsonConfig = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			default:
				iNdEx = preIndex
				skippy, err := runtime.Skip(dAtA[iNdEx:])
				if err != nil {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, err
				}
				if (skippy < 0) || (iNdEx+skippy) < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if (iNdEx + skippy) > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				if !options.DiscardUnknown {
					x.unknownFields = append(x.unknownFields, dAtA[iNdEx:iNdEx+skippy]...)
				}
				iNdEx += skippy
			}
		}

		if iNdEx > l {
			return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
		}
		return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, nil
	}
	return &protoiface.Methods{
		NoUnkeyedLiterals: struct{}{},
		Flags:             protoiface.SupportMarshalDeterministic | protoiface.SupportUnmarshalDiscardUnknown,
		Size:              size,
		Marshal:           marshal,
		Unmarshal:         unmarshal,
		Merge:             nil,
		CheckInitialized:  nil,
	}
}

var (
	md_Preinstall         protoreflect.MessageDescriptor
	fd_Preinstall_name    protoreflect.FieldDescriptor
	fd_Preinstall_address protoreflect.FieldDescriptor
	fd_Preinstall_code    protoreflect.FieldDescriptor
)

func init() {
	file_cosmos_evm_vm_v1_evm_proto_init()
	md_Preinstall = File_cosmos_evm_vm_v1_evm_proto.Messages().ByName("Preinstall")
	fd_Preinstall_name = md_Preinstall.Fields().ByName("name")
	fd_Preinstall_address = md_Preinstall.Fields().ByName("address")
	fd_Preinstall_code = md_Preinstall.Fields().ByName("code")
}

var _ protoreflect.Message = (*fastReflection_Preinstall)(nil)

type fastReflection_Preinstall Preinstall

func (x *Preinstall) ProtoReflect() protoreflect.Message {
	return (*fastReflection_Preinstall)(x)
}

func (x *Preinstall) slowProtoReflect() protoreflect.Message {
	mi := &file_cosmos_evm_vm_v1_evm_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

var _fastReflection_Preinstall_messageType fastReflection_Preinstall_messageType
var _ protoreflect.MessageType = fastReflection_Preinstall_messageType{}

type fastReflection_Preinstall_messageType struct{}

func (x fastReflection_Preinstall_messageType) Zero() protoreflect.Message {
	return (*fastReflection_Preinstall)(nil)
}
func (x fastReflection_Preinstall_messageType) New() protoreflect.Message {
	return new(fastReflection_Preinstall)
}
func (x fastReflection_Preinstall_messageType) Descriptor() protoreflect.MessageDescriptor {
	return md_Preinstall
}

// Descriptor returns message descriptor, which contains only the protobuf
// type information for the message.
func (x *fastReflection_Preinstall) Descriptor() protoreflect.MessageDescriptor {
	return md_Preinstall
}

// Type returns the message type, which encapsulates both Go and protobuf
// type information. If the Go type information is not needed,
// it is recommended that the message descriptor be used instead.
func (x *fastReflection_Preinstall) Type() protoreflect.MessageType {
	return _fastReflection_Preinstall_messageType
}

// New returns a newly allocated and mutable empty message.
func (x *fastReflection_Preinstall) New() protoreflect.Message {
	return new(fastReflection_Preinstall)
}

// Interface unwraps the message reflection interface and
// returns the underlying ProtoMessage interface.
func (x *fastReflection_Preinstall) Interface() protoreflect.ProtoMessage {
	return (*Preinstall)(x)
}

// Range iterates over every populated field in an undefined order,
// calling f for each field descriptor and value encountered.
// Range returns immediately if f returns false.
// While iterating, mutating operations may only be performed
// on the current field descriptor.
func (x *fastReflection_Preinstall) Range(f func(protoreflect.FieldDescriptor, protoreflect.Value) bool) {
	if x.Name != "" {
		value := protoreflect.ValueOfString(x.Name)
		if !f(fd_Preinstall_name, value) {
			return
		}
	}
	if x.Address != "" {
		value := protoreflect.ValueOfString(x.Address)
		if !f(fd_Preinstall_address, value) {
			return
		}
	}
	if x.Code != "" {
		value := protoreflect.ValueOfString(x.Code)
		if !f(fd_Preinstall_code, value) {
			return
		}
	}
}

// Has reports whether a field is populated.
//
// Some fields have the property of nullability where it is possible to
// distinguish between the default value of a field and whether the field
// was explicitly populated with the default value. Singular message fields,
// member fields of a oneof, and proto2 scalar fields are nullable. Such
// fields are populated only if explicitly set.
//
// In other cases (aside from the nullable cases above),
// a proto3 scalar field is populated if it contains a non-zero value, and
// a repeated field is populated if it is non-empty.
func (x *fastReflection_Preinstall) Has(fd protoreflect.FieldDescriptor) bool {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.Preinstall.name":
		return x.Name != ""
	case "cosmos.evm.vm.v1.Preinstall.address":
		return x.Address != ""
	case "cosmos.evm.vm.v1.Preinstall.code":
		return x.Code != ""
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.Preinstall"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.Preinstall does not contain field %s", fd.FullName()))
	}
}

// Clear clears the field such that a subsequent Has call reports false.
//
// Clearing an extension field clears both the extension type and value
// associated with the given field number.
//
// Clear is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_Preinstall) Clear(fd protoreflect.FieldDescriptor) {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.Preinstall.name":
		x.Name = ""
	case "cosmos.evm.vm.v1.Preinstall.address":
		x.Address = ""
	case "cosmos.evm.vm.v1.Preinstall.code":
		x.Code = ""
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.Preinstall"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.Preinstall does not contain field %s", fd.FullName()))
	}
}

// Get retrieves the value for a field.
//
// For unpopulated scalars, it returns the default value, where
// the default value of a bytes scalar is guaranteed to be a copy.
// For unpopulated composite types, it returns an empty, read-only view
// of the value; to obtain a mutable reference, use Mutable.
func (x *fastReflection_Preinstall) Get(descriptor protoreflect.FieldDescriptor) protoreflect.Value {
	switch descriptor.FullName() {
	case "cosmos.evm.vm.v1.Preinstall.name":
		value := x.Name
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.Preinstall.address":
		value := x.Address
		return protoreflect.ValueOfString(value)
	case "cosmos.evm.vm.v1.Preinstall.code":
		value := x.Code
		return protoreflect.ValueOfString(value)
	default:
		if descriptor.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.Preinstall"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.Preinstall does not contain field %s", descriptor.FullName()))
	}
}

// Set stores the value for a field.
//
// For a field belonging to a oneof, it implicitly clears any other field
// that may be currently set within the same oneof.
// For extension fields, it implicitly stores the provided ExtensionType.
// When setting a composite type, it is unspecified whether the stored value
// aliases the source's memory in any way. If the composite value is an
// empty, read-only value, then it panics.
//
// Set is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_Preinstall) Set(fd protoreflect.FieldDescriptor, value protoreflect.Value) {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.Preinstall.name":
		x.Name = value.Interface().(string)
	case "cosmos.evm.vm.v1.Preinstall.address":
		x.Address = value.Interface().(string)
	case "cosmos.evm.vm.v1.Preinstall.code":
		x.Code = value.Interface().(string)
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.Preinstall"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.Preinstall does not contain field %s", fd.FullName()))
	}
}

// Mutable returns a mutable reference to a composite type.
//
// If the field is unpopulated, it may allocate a composite value.
// For a field belonging to a oneof, it implicitly clears any other field
// that may be currently set within the same oneof.
// For extension fields, it implicitly stores the provided ExtensionType
// if not already stored.
// It panics if the field does not contain a composite type.
//
// Mutable is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_Preinstall) Mutable(fd protoreflect.FieldDescriptor) protoreflect.Value {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.Preinstall.name":
		panic(fmt.Errorf("field name of message cosmos.evm.vm.v1.Preinstall is not mutable"))
	case "cosmos.evm.vm.v1.Preinstall.address":
		panic(fmt.Errorf("field address of message cosmos.evm.vm.v1.Preinstall is not mutable"))
	case "cosmos.evm.vm.v1.Preinstall.code":
		panic(fmt.Errorf("field code of message cosmos.evm.vm.v1.Preinstall is not mutable"))
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.Preinstall"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.Preinstall does not contain field %s", fd.FullName()))
	}
}

// NewField returns a new value that is assignable to the field
// for the given descriptor. For scalars, this returns the default value.
// For lists, maps, and messages, this returns a new, empty, mutable value.
func (x *fastReflection_Preinstall) NewField(fd protoreflect.FieldDescriptor) protoreflect.Value {
	switch fd.FullName() {
	case "cosmos.evm.vm.v1.Preinstall.name":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.Preinstall.address":
		return protoreflect.ValueOfString("")
	case "cosmos.evm.vm.v1.Preinstall.code":
		return protoreflect.ValueOfString("")
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.vm.v1.Preinstall"))
		}
		panic(fmt.Errorf("message cosmos.evm.vm.v1.Preinstall does not contain field %s", fd.FullName()))
	}
}

// WhichOneof reports which field within the oneof is populated,
// returning nil if none are populated.
// It panics if the oneof descriptor does not belong to this message.
func (x *fastReflection_Preinstall) WhichOneof(d protoreflect.OneofDescriptor) protoreflect.FieldDescriptor {
	switch d.FullName() {
	default:
		panic(fmt.Errorf("%s is not a oneof field in cosmos.evm.vm.v1.Preinstall", d.FullName()))
	}
	panic("unreachable")
}

// GetUnknown retrieves the entire list of unknown fields.
// The caller may only mutate the contents of the RawFields
// if the mutated bytes are stored back into the message with SetUnknown.
func (x *fastReflection_Preinstall) GetUnknown() protoreflect.RawFields {
	return x.unknownFields
}

// SetUnknown stores an entire list of unknown fields.
// The raw fields must be syntactically valid according to the wire format.
// An implementation may panic if this is not the case.
// Once stored, the caller must not mutate the content of the RawFields.
// An empty RawFields may be passed to clear the fields.
//
// SetUnknown is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_Preinstall) SetUnknown(fields protoreflect.RawFields) {
	x.unknownFields = fields
}

// IsValid reports whether the message is valid.
//
// An invalid message is an empty, read-only value.
//
// An invalid message often corresponds to a nil pointer of the concrete
// message type, but the details are implementation dependent.
// Validity is not part of the protobuf data model, and may not
// be preserved in marshaling or other operations.
func (x *fastReflection_Preinstall) IsValid() bool {
	return x != nil
}

// ProtoMethods returns optional fastReflectionFeature-path implementations of various operations.
// This method may return nil.
//
// The returned methods type is identical to
// "google.golang.org/protobuf/runtime/protoiface".Methods.
// Consult the protoiface package documentation for details.
func (x *fastReflection_Preinstall) ProtoMethods() *protoiface.Methods {
	size := func(input protoiface.SizeInput) protoiface.SizeOutput {
		x := input.Message.Interface().(*Preinstall)
		if x == nil {
			return protoiface.SizeOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Size:              0,
			}
		}
		options := runtime.SizeInputToOptions(input)
		_ = options
		var n int
		var l int
		_ = l
		l = len(x.Name)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		l = len(x.Address)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		l = len(x.Code)
		if l > 0 {
			n += 1 + l + runtime.Sov(uint64(l))
		}
		if x.unknownFields != nil {
			n += len(x.unknownFields)
		}
		return protoiface.SizeOutput{
			NoUnkeyedLiterals: input.NoUnkeyedLiterals,
			Size:              n,
		}
	}

	marshal := func(input protoiface.MarshalInput) (protoiface.MarshalOutput, error) {
		x := input.Message.Interface().(*Preinstall)
		if x == nil {
			return protoiface.MarshalOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Buf:               input.Buf,
			}, nil
		}
		options := runtime.MarshalInputToOptions(input)
		_ = options
		size := options.Size(x)
		dAtA := make([]byte, size)
		i := len(dAtA)
		_ = i
		var l int
		_ = l
		if x.unknownFields != nil {
			i -= len(x.unknownFields)
			copy(dAtA[i:], x.unknownFields)
		}
		if len(x.Code) > 0 {
			i -= len(x.Code)
			copy(dAtA[i:], x.Code)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.Code)))
			i--
			dAtA[i] = 0x1a
		}
		if len(x.Address) > 0 {
			i -= len(x.Address)
			copy(dAtA[i:], x.Address)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.Address)))
			i--
			dAtA[i] = 0x12
		}
		if len(x.Name) > 0 {
			i -= len(x.Name)
			copy(dAtA[i:], x.Name)
			i = runtime.EncodeVarint(dAtA, i, uint64(len(x.Name)))
			i--
			dAtA[i] = 0xa
		}
		if input.Buf != nil {
			input.Buf = append(input.Buf, dAtA...)
		} else {
			input.Buf = dAtA
		}
		return protoiface.MarshalOutput{
			NoUnkeyedLiterals: input.NoUnkeyedLiterals,
			Buf:               input.Buf,
		}, nil
	}
	unmarshal := func(input protoiface.UnmarshalInput) (protoiface.UnmarshalOutput, error) {
		x := input.Message.Interface().(*Preinstall)
		if x == nil {
			return protoiface.UnmarshalOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Flags:             input.Flags,
			}, nil
		}
		options := runtime.UnmarshalInputToOptions(input)
		_ = options
		dAtA := input.Buf
		l := len(dAtA)
		iNdEx := 0
		for iNdEx < l {
			preIndex := iNdEx
			var wire uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
				}
				if iNdEx >= l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				wire |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			fieldNum := int32(wire >> 3)
			wireType := int(wire & 0x7)
			if wireType == 4 {
				return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: Preinstall: wiretype end group for non-group")
			}
			if fieldNum <= 0 {
				return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: Preinstall: illegal tag %d (wire type %d)", fieldNum, wire)
			}
			switch fieldNum {
			case 1:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.Name = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 2:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Address", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.Address = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			case 3:
				if wireType != 2 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Code", wireType)
				}
				var stringLen uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					stringLen |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				intStringLen := int(stringLen)
				if intStringLen < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				postIndex := iNdEx + intStringLen
				if postIndex < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if postIndex > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				x.Code = string(dAtA[iNdEx:postIndex])
				iNdEx = postIndex
			default:
				iNdEx = preIndex
				skippy, err := runtime.Skip(dAtA[iNdEx:])
				if err != nil {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, err
				}
				if (skippy < 0) || (iNdEx+skippy) < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if (iNdEx + skippy) > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				if !options.DiscardUnknown {
					x.unknownFields = append(x.unknownFields, dAtA[iNdEx:iNdEx+skippy]...)
				}
				iNdEx += skippy
			}
		}

		if iNdEx > l {
			return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
		}
		return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, nil
	}
	return &protoiface.Methods{
		NoUnkeyedLiterals: struct{}{},
		Flags:             protoiface.SupportMarshalDeterministic | protoiface.SupportUnmarshalDiscardUnknown,
		Size:              size,
		Marshal:           marshal,
		Unmarshal:         unmarshal,
		Merge:             nil,
		CheckInitialized:  nil,
	}
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.27.0
// 	protoc        (unknown)
// source: cosmos/evm/vm/v1/evm.proto

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AccessType defines the types of permissions for the operations
type AccessType int32

const (
	// ACCESS_TYPE_PERMISSIONLESS does not restrict the operation to anyone
	AccessType_ACCESS_TYPE_PERMISSIONLESS AccessType = 0
	// ACCESS_TYPE_RESTRICTED restrict the operation to anyone
	AccessType_ACCESS_TYPE_RESTRICTED AccessType = 1
	// ACCESS_TYPE_PERMISSIONED only allows the operation for specific addresses
	AccessType_ACCESS_TYPE_PERMISSIONED AccessType = 2
)

// Enum value maps for AccessType.
var (
	AccessType_name = map[int32]string{
		0: "ACCESS_TYPE_PERMISSIONLESS",
		1: "ACCESS_TYPE_RESTRICTED",
		2: "ACCESS_TYPE_PERMISSIONED",
	}
	AccessType_value = map[string]int32{
		"ACCESS_TYPE_PERMISSIONLESS": 0,
		"ACCESS_TYPE_RESTRICTED":     1,
		"ACCESS_TYPE_PERMISSIONED":   2,
	}
)

func (x AccessType) Enum() *AccessType {
	p := new(AccessType)
	*p = x
	return p
}

func (x AccessType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AccessType) Descriptor() protoreflect.EnumDescriptor {
	return file_cosmos_evm_vm_v1_evm_proto_enumTypes[0].Descriptor()
}

func (AccessType) Type() protoreflect.EnumType {
	return &file_cosmos_evm_vm_v1_evm_proto_enumTypes[0]
}

func (x AccessType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AccessType.Descriptor instead.
func (AccessType) EnumDescriptor() ([]byte, []int) {
	return file_cosmos_evm_vm_v1_evm_proto_rawDescGZIP(), []int{0}
}

// Params defines the EVM module parameters
type Params struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// evm_denom represents the token denomination used to run the EVM state
	// transitions.
	EvmDenom string `protobuf:"bytes,1,opt,name=evm_denom,json=evmDenom,proto3" json:"evm_denom,omitempty"`
	// extra_eips defines the additional EIPs for the vm.Config
	ExtraEips []int64 `protobuf:"varint,4,rep,packed,name=extra_eips,json=extraEips,proto3" json:"extra_eips,omitempty"`
	// evm_channels is the list of channel identifiers from EVM compatible chains
	EvmChannels []string `protobuf:"bytes,7,rep,name=evm_channels,json=evmChannels,proto3" json:"evm_channels,omitempty"`
	// access_control defines the permission policy of the EVM
	AccessControl *AccessControl `protobuf:"bytes,8,opt,name=access_control,json=accessControl,proto3" json:"access_control,omitempty"`
	// active_static_precompiles defines the slice of hex addresses of the
	// precompiled contracts that are active
	ActiveStaticPrecompiles []string `protobuf:"bytes,9,rep,name=active_static_precompiles,json=activeStaticPrecompiles,proto3" json:"active_static_precompiles,omitempty"`
	HistoryServeWindow      uint64   `protobuf:"varint,10,opt,name=history_serve_window,json=historyServeWindow,proto3" json:"history_serve_window,omitempty"`
}

func (x *Params) Reset() {
	*x = Params{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cosmos_evm_vm_v1_evm_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Params) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Params) ProtoMessage() {}

// Deprecated: Use Params.ProtoReflect.Descriptor instead.
func (*Params) Descriptor() ([]byte, []int) {
	return file_cosmos_evm_vm_v1_evm_proto_rawDescGZIP(), []int{0}
}

func (x *Params) GetEvmDenom() string {
	if x != nil {
		return x.EvmDenom
	}
	return ""
}

func (x *Params) GetExtraEips() []int64 {
	if x != nil {
		return x.ExtraEips
	}
	return nil
}

func (x *Params) GetEvmChannels() []string {
	if x != nil {
		return x.EvmChannels
	}
	return nil
}

func (x *Params) GetAccessControl() *AccessControl {
	if x != nil {
		return x.AccessControl
	}
	return nil
}

func (x *Params) GetActiveStaticPrecompiles() []string {
	if x != nil {
		return x.ActiveStaticPrecompiles
	}
	return nil
}

func (x *Params) GetHistoryServeWindow() uint64 {
	if x != nil {
		return x.HistoryServeWindow
	}
	return 0
}

// AccessControl defines the permission policy of the EVM
// for creating and calling contracts
type AccessControl struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// create defines the permission policy for creating contracts
	Create *AccessControlType `protobuf:"bytes,1,opt,name=create,proto3" json:"create,omitempty"`
	// call defines the permission policy for calling contracts
	Call *AccessControlType `protobuf:"bytes,2,opt,name=call,proto3" json:"call,omitempty"`
}

func (x *AccessControl) Reset() {
	*x = AccessControl{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cosmos_evm_vm_v1_evm_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccessControl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccessControl) ProtoMessage() {}

// Deprecated: Use AccessControl.ProtoReflect.Descriptor instead.
func (*AccessControl) Descriptor() ([]byte, []int) {
	return file_cosmos_evm_vm_v1_evm_proto_rawDescGZIP(), []int{1}
}

func (x *AccessControl) GetCreate() *AccessControlType {
	if x != nil {
		return x.Create
	}
	return nil
}

func (x *AccessControl) GetCall() *AccessControlType {
	if x != nil {
		return x.Call
	}
	return nil
}

// AccessControlType defines the permission type for policies
type AccessControlType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// access_type defines which type of permission is required for the operation
	AccessType AccessType `protobuf:"varint,1,opt,name=access_type,json=accessType,proto3,enum=cosmos.evm.vm.v1.AccessType" json:"access_type,omitempty"`
	// access_control_list defines defines different things depending on the
	// AccessType:
	// - ACCESS_TYPE_PERMISSIONLESS: list of addresses that are blocked from
	// performing the operation
	// - ACCESS_TYPE_RESTRICTED: ignored
	// - ACCESS_TYPE_PERMISSIONED: list of addresses that are allowed to perform
	// the operation
	AccessControlList []string `protobuf:"bytes,2,rep,name=access_control_list,json=accessControlList,proto3" json:"access_control_list,omitempty"`
}

func (x *AccessControlType) Reset() {
	*x = AccessControlType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cosmos_evm_vm_v1_evm_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccessControlType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccessControlType) ProtoMessage() {}

// Deprecated: Use AccessControlType.ProtoReflect.Descriptor instead.
func (*AccessControlType) Descriptor() ([]byte, []int) {
	return file_cosmos_evm_vm_v1_evm_proto_rawDescGZIP(), []int{2}
}

func (x *AccessControlType) GetAccessType() AccessType {
	if x != nil {
		return x.AccessType
	}
	return AccessType_ACCESS_TYPE_PERMISSIONLESS
}

func (x *AccessControlType) GetAccessControlList() []string {
	if x != nil {
		return x.AccessControlList
	}
	return nil
}

// ChainConfig defines the Ethereum ChainConfig parameters using *sdk.Int values
// instead of *big.Int.
type ChainConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// homestead_block switch (nil no fork, 0 = already homestead)
	HomesteadBlock string `protobuf:"bytes,1,opt,name=homestead_block,json=homesteadBlock,proto3" json:"homestead_block,omitempty"`
	// dao_fork_block corresponds to TheDAO hard-fork switch block (nil no fork)
	DaoForkBlock string `protobuf:"bytes,2,opt,name=dao_fork_block,json=daoForkBlock,proto3" json:"dao_fork_block,omitempty"`
	// dao_fork_support defines whether the nodes supports or opposes the DAO
	// hard-fork
	DaoForkSupport bool `protobuf:"varint,3,opt,name=dao_fork_support,json=daoForkSupport,proto3" json:"dao_fork_support,omitempty"`
	// eip150_block: EIP150 implements the Gas price changes
	// (https://github.com/ethereum/EIPs/issues/150) EIP150 HF block (nil no fork)
	Eip150Block string `protobuf:"bytes,4,opt,name=eip150_block,json=eip150Block,proto3" json:"eip150_block,omitempty"`
	// eip155_block: EIP155Block HF block
	Eip155Block string `protobuf:"bytes,6,opt,name=eip155_block,json=eip155Block,proto3" json:"eip155_block,omitempty"`
	// eip158_block: EIP158 HF block
	Eip158Block string `protobuf:"bytes,7,opt,name=eip158_block,json=eip158Block,proto3" json:"eip158_block,omitempty"`
	// byzantium_block: Byzantium switch block (nil no fork, 0 = already on
	// byzantium)
	ByzantiumBlock string `protobuf:"bytes,8,opt,name=byzantium_block,json=byzantiumBlock,proto3" json:"byzantium_block,omitempty"`
	// constantinople_block: Constantinople switch block (nil no fork, 0 = already
	// activated)
	ConstantinopleBlock string `protobuf:"bytes,9,opt,name=constantinople_block,json=constantinopleBlock,proto3" json:"constantinople_block,omitempty"`
	// petersburg_block: Petersburg switch block (nil same as Constantinople)
	PetersburgBlock string `protobuf:"bytes,10,opt,name=petersburg_block,json=petersburgBlock,proto3" json:"petersburg_block,omitempty"`
	// istanbul_block: Istanbul switch block (nil no fork, 0 = already on
	// istanbul)
	IstanbulBlock string `protobuf:"bytes,11,opt,name=istanbul_block,json=istanbulBlock,proto3" json:"istanbul_block,omitempty"`
	// muir_glacier_block: Eip-2384 (bomb delay) switch block (nil no fork, 0 =
	// already activated)
	MuirGlacierBlock string `protobuf:"bytes,12,opt,name=muir_glacier_block,json=muirGlacierBlock,proto3" json:"muir_glacier_block,omitempty"`
	// berlin_block: Berlin switch block (nil = no fork, 0 = already on berlin)
	BerlinBlock string `protobuf:"bytes,13,opt,name=berlin_block,json=berlinBlock,proto3" json:"berlin_block,omitempty"`
	// london_block: London switch block (nil = no fork, 0 = already on london)
	LondonBlock string `protobuf:"bytes,17,opt,name=london_block,json=londonBlock,proto3" json:"london_block,omitempty"`
	// arrow_glacier_block: Eip-4345 (bomb delay) switch block (nil = no fork, 0 =
	// already activated)
	ArrowGlacierBlock string `protobuf:"bytes,18,opt,name=arrow_glacier_block,json=arrowGlacierBlock,proto3" json:"arrow_glacier_block,omitempty"`
	// gray_glacier_block: EIP-5133 (bomb delay) switch block (nil = no fork, 0 =
	// already activated)
	GrayGlacierBlock string `protobuf:"bytes,20,opt,name=gray_glacier_block,json=grayGlacierBlock,proto3" json:"gray_glacier_block,omitempty"`
	// merge_netsplit_block: Virtual fork after The Merge to use as a network
	// splitter
	MergeNetsplitBlock string `protobuf:"bytes,21,opt,name=merge_netsplit_block,json=mergeNetsplitBlock,proto3" json:"merge_netsplit_block,omitempty"`
	// chain_id is the id of the chain (EIP-155)
	ChainId uint64 `protobuf:"varint,24,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
	// denom is the denomination used on the EVM
	Denom string `protobuf:"bytes,25,opt,name=denom,proto3" json:"denom,omitempty"`
	// decimals is the real decimal precision of the denomination used on the EVM
	Decimals uint64 `protobuf:"varint,26,opt,name=decimals,proto3" json:"decimals,omitempty"`
	// shanghai_time: Shanghai switch time (nil = no fork, 0 = already on
	// shanghai)
	ShanghaiTime string `protobuf:"bytes,27,opt,name=shanghai_time,json=shanghaiTime,proto3" json:"shanghai_time,omitempty"`
	// cancun_time: Cancun switch time (nil = no fork, 0 = already on cancun)
	CancunTime string `protobuf:"bytes,28,opt,name=cancun_time,json=cancunTime,proto3" json:"cancun_time,omitempty"`
	// prague_time: Prague switch time (nil = no fork, 0 = already on prague)
	PragueTime string `protobuf:"bytes,29,opt,name=prague_time,json=pragueTime,proto3" json:"prague_time,omitempty"`
	// verkle_time: Verkle switch time (nil = no fork, 0 = already on verkle)
	VerkleTime string `protobuf:"bytes,30,opt,name=verkle_time,json=verkleTime,proto3" json:"verkle_time,omitempty"`
	// osaka_time: Osaka switch time (nil = no fork, 0 = already on osaka)
	OsakaTime string `protobuf:"bytes,31,opt,name=osaka_time,json=osakaTime,proto3" json:"osaka_time,omitempty"`
}

func (x *ChainConfig) Reset() {
	*x = ChainConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cosmos_evm_vm_v1_evm_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChainConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChainConfig) ProtoMessage() {}

// Deprecated: Use ChainConfig.ProtoReflect.Descriptor instead.
func (*ChainConfig) Descriptor() ([]byte, []int) {
	return file_cosmos_evm_vm_v1_evm_proto_rawDescGZIP(), []int{3}
}

func (x *ChainConfig) GetHomesteadBlock() string {
	if x != nil {
		return x.HomesteadBlock
	}
	return ""
}

func (x *ChainConfig) GetDaoForkBlock() string {
	if x != nil {
		return x.DaoForkBlock
	}
	return ""
}

func (x *ChainConfig) GetDaoForkSupport() bool {
	if x != nil {
		return x.DaoForkSupport
	}
	return false
}

func (x *ChainConfig) GetEip150Block() string {
	if x != nil {
		return x.Eip150Block
	}
	return ""
}

func (x *ChainConfig) GetEip155Block() string {
	if x != nil {
		return x.Eip155Block
	}
	return ""
}

func (x *ChainConfig) GetEip158Block() string {
	if x != nil {
		return x.Eip158Block
	}
	return ""
}

func (x *ChainConfig) GetByzantiumBlock() string {
	if x != nil {
		return x.ByzantiumBlock
	}
	return ""
}

func (x *ChainConfig) GetConstantinopleBlock() string {
	if x != nil {
		return x.ConstantinopleBlock
	}
	return ""
}

func (x *ChainConfig) GetPetersburgBlock() string {
	if x != nil {
		return x.PetersburgBlock
	}
	return ""
}

func (x *ChainConfig) GetIstanbulBlock() string {
	if x != nil {
		return x.IstanbulBlock
	}
	return ""
}

func (x *ChainConfig) GetMuirGlacierBlock() string {
	if x != nil {
		return x.MuirGlacierBlock
	}
	return ""
}

func (x *ChainConfig) GetBerlinBlock() string {
	if x != nil {
		return x.BerlinBlock
	}
	return ""
}

func (x *ChainConfig) GetLondonBlock() string {
	if x != nil {
		return x.LondonBlock
	}
	return ""
}

func (x *ChainConfig) GetArrowGlacierBlock() string {
	if x != nil {
		return x.ArrowGlacierBlock
	}
	return ""
}

func (x *ChainConfig) GetGrayGlacierBlock() string {
	if x != nil {
		return x.GrayGlacierBlock
	}
	return ""
}

func (x *ChainConfig) GetMergeNetsplitBlock() string {
	if x != nil {
		return x.MergeNetsplitBlock
	}
	return ""
}

func (x *ChainConfig) GetChainId() uint64 {
	if x != nil {
		return x.ChainId
	}
	return 0
}

func (x *ChainConfig) GetDenom() string {
	if x != nil {
		return x.Denom
	}
	return ""
}

func (x *ChainConfig) GetDecimals() uint64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *ChainConfig) GetShanghaiTime() string {
	if x != nil {
		return x.ShanghaiTime
	}
	return ""
}

func (x *ChainConfig) GetCancunTime() string {
	if x != nil {
		return x.CancunTime
	}
	return ""
}

func (x *ChainConfig) GetPragueTime() string {
	if x != nil {
		return x.PragueTime
	}
	return ""
}

func (x *ChainConfig) GetVerkleTime() string {
	if x != nil {
		return x.VerkleTime
	}
	return ""
}

func (x *ChainConfig) GetOsakaTime() string {
	if x != nil {
		return x.OsakaTime
	}
	return ""
}

// State represents a single Storage key value pair item.
type State struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// key is the stored key
	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	// value is the stored value for the given key
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *State) Reset() {
	*x = State{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cosmos_evm_vm_v1_evm_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *State) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*State) ProtoMessage() {}

// Deprecated: Use State.ProtoReflect.Descriptor instead.
func (*State) Descriptor() ([]byte, []int) {
	return file_cosmos_evm_vm_v1_evm_proto_rawDescGZIP(), []int{4}
}

func (x *State) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *State) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// TransactionLogs define the logs generated from a transaction execution
// with a given hash. It it used for import/export data as transactions are not
// persisted on blockchain state after an upgrade.
type TransactionLogs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// hash of the transaction
	Hash string `protobuf:"bytes,1,opt,name=hash,proto3" json:"hash,omitempty"`
	// logs is an array of Logs for the given transaction hash
	Logs []*Log `protobuf:"bytes,2,rep,name=logs,proto3" json:"logs,omitempty"`
}

func (x *TransactionLogs) Reset() {
	*x = TransactionLogs{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cosmos_evm_vm_v1_evm_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransactionLogs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionLogs) ProtoMessage() {}

// Deprecated: Use TransactionLogs.ProtoReflect.Descriptor instead.
func (*TransactionLogs) Descriptor() ([]byte, []int) {
	return file_cosmos_evm_vm_v1_evm_proto_rawDescGZIP(), []int{5}
}

func (x *TransactionLogs) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *TransactionLogs) GetLogs() []*Log {
	if x != nil {
		return x.Logs
	}
	return nil
}

// Log represents an protobuf compatible Ethereum Log that defines a contract
// log event. These events are generated by the LOG opcode and stored/indexed by
// the node.
//
// NOTE: address, topics and data are consensus fields. The rest of the fields
// are derived, i.e. filled in by the nodes, but not secured by consensus.
type Log struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// address of the contract that generated the event
	Address string `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	// topics is a list of topics provided by the contract.
	Topics []string `protobuf:"bytes,2,rep,name=topics,proto3" json:"topics,omitempty"`
	// data which is supplied by the contract, usually ABI-encoded
	Data []byte `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	// block_number of the block in which the transaction was included
	BlockNumber uint64 `protobuf:"varint,4,opt,name=block_number,json=blockNumber,proto3" json:"block_number,omitempty"`
	// tx_hash is the transaction hash
	TxHash string `protobuf:"bytes,5,opt,name=tx_hash,json=txHash,proto3" json:"tx_hash,omitempty"`
	// tx_index of the transaction in the block
	TxIndex uint64 `protobuf:"varint,6,opt,name=tx_index,json=txIndex,proto3" json:"tx_index,omitempty"`
	// block_hash of the block in which the transaction was included
	BlockHash string `protobuf:"bytes,7,opt,name=block_hash,json=blockHash,proto3" json:"block_hash,omitempty"`
	// index of the log in the block
	Index uint64 `protobuf:"varint,8,opt,name=index,proto3" json:"index,omitempty"`
	// removed is true if this log was reverted due to a chain
	// reorganisation. You must pay attention to this field if you receive logs
	// through a filter query.
	Removed bool `protobuf:"varint,9,opt,name=removed,proto3" json:"removed,omitempty"`
	// block_timestamp is the timestamp of the block in which the transaction was
	BlockTimestamp uint64 `protobuf:"varint,10,opt,name=block_timestamp,json=blockTimestamp,proto3" json:"block_timestamp,omitempty"`
}

func (x *Log) Reset() {
	*x = Log{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cosmos_evm_vm_v1_evm_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Log) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Log) ProtoMessage() {}

// Deprecated: Use Log.ProtoReflect.Descriptor instead.
func (*Log) Descriptor() ([]byte, []int) {
	return file_cosmos_evm_vm_v1_evm_proto_rawDescGZIP(), []int{6}
}

func (x *Log) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *Log) GetTopics() []string {
	if x != nil {
		return x.Topics
	}
	return nil
}

func (x *Log) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Log) GetBlockNumber() uint64 {
	if x != nil {
		return x.BlockNumber
	}
	return 0
}

func (x *Log) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

func (x *Log) GetTxIndex() uint64 {
	if x != nil {
		return x.TxIndex
	}
	return 0
}

func (x *Log) GetBlockHash() string {
	if x != nil {
		return x.BlockHash
	}
	return ""
}

func (x *Log) GetIndex() uint64 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *Log) GetRemoved() bool {
	if x != nil {
		return x.Removed
	}
	return false
}

func (x *Log) GetBlockTimestamp() uint64 {
	if x != nil {
		return x.BlockTimestamp
	}
	return 0
}

// TxResult stores results of Tx execution.
type TxResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// contract_address contains the ethereum address of the created contract (if
	// any). If the state transition is an evm.Call, the contract address will be
	// empty.
	ContractAddress string `protobuf:"bytes,1,opt,name=contract_address,json=contractAddress,proto3" json:"contract_address,omitempty"`
	// bloom represents the bloom filter bytes
	Bloom []byte `protobuf:"bytes,2,opt,name=bloom,proto3" json:"bloom,omitempty"`
	// tx_logs contains the transaction hash and the proto-compatible ethereum
	// logs.
	TxLogs *TransactionLogs `protobuf:"bytes,3,opt,name=tx_logs,json=txLogs,proto3" json:"tx_logs,omitempty"`
	// ret defines the bytes from the execution.
	Ret []byte `protobuf:"bytes,4,opt,name=ret,proto3" json:"ret,omitempty"`
	// reverted flag is set to true when the call has been reverted
	Reverted bool `protobuf:"varint,5,opt,name=reverted,proto3" json:"reverted,omitempty"`
	// gas_used notes the amount of gas consumed while execution
	GasUsed uint64 `protobuf:"varint,6,opt,name=gas_used,json=gasUsed,proto3" json:"gas_used,omitempty"`
}

func (x *TxResult) Reset() {
	*x = TxResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cosmos_evm_vm_v1_evm_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TxResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxResult) ProtoMessage() {}

// Deprecated: Use TxResult.ProtoReflect.Descriptor instead.
func (*TxResult) Descriptor() ([]byte, []int) {
	return file_cosmos_evm_vm_v1_evm_proto_rawDescGZIP(), []int{7}
}

func (x *TxResult) GetContractAddress() string {
	if x != nil {
		return x.ContractAddress
	}
	return ""
}

func (x *TxResult) GetBloom() []byte {
	if x != nil {
		return x.Bloom
	}
	return nil
}

func (x *TxResult) GetTxLogs() *TransactionLogs {
	if x != nil {
		return x.TxLogs
	}
	return nil
}

func (x *TxResult) GetRet() []byte {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *TxResult) GetReverted() bool {
	if x != nil {
		return x.Reverted
	}
	return false
}

func (x *TxResult) GetGasUsed() uint64 {
	if x != nil {
		return x.GasUsed
	}
	return 0
}

// AccessTuple is the element type of an access list.
type AccessTuple struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// address is a hex formatted ethereum address
	Address string `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	// storage_keys are hex formatted hashes of the storage keys
	StorageKeys []string `protobuf:"bytes,2,rep,name=storage_keys,json=storageKeys,proto3" json:"storage_keys,omitempty"`
}

func (x *AccessTuple) Reset() {
	*x = AccessTuple{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cosmos_evm_vm_v1_evm_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccessTuple) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccessTuple) ProtoMessage() {}

// Deprecated: Use AccessTuple.ProtoReflect.Descriptor instead.
func (*AccessTuple) Descriptor() ([]byte, []int) {
	return file_cosmos_evm_vm_v1_evm_proto_rawDescGZIP(), []int{8}
}

func (x *AccessTuple) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *AccessTuple) GetStorageKeys() []string {
	if x != nil {
		return x.StorageKeys
	}
	return nil
}

// TraceConfig holds extra parameters to trace functions.
type TraceConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tracer is a custom javascript tracer
	Tracer string `protobuf:"bytes,1,opt,name=tracer,proto3" json:"tracer,omitempty"`
	// timeout overrides the default timeout of 5 seconds for JavaScript-based
	// tracing calls
	Timeout string `protobuf:"bytes,2,opt,name=timeout,proto3" json:"timeout,omitempty"`
	// reexec defines the number of blocks the tracer is willing to go back
	Reexec uint64 `protobuf:"varint,3,opt,name=reexec,proto3" json:"reexec,omitempty"`
	// disable_stack switches stack capture
	DisableStack bool `protobuf:"varint,5,opt,name=disable_stack,json=disableStack,proto3" json:"disable_stack,omitempty"`
	// disable_storage switches storage capture
	DisableStorage bool `protobuf:"varint,6,opt,name=disable_storage,json=disableStorage,proto3" json:"disable_storage,omitempty"`
	// debug can be used to print output during capture end
	Debug bool `protobuf:"varint,8,opt,name=debug,proto3" json:"debug,omitempty"`
	// limit defines the maximum length of output, but zero means unlimited
	Limit int32 `protobuf:"varint,9,opt,name=limit,proto3" json:"limit,omitempty"`
	// overrides can be used to execute a trace using future fork rules
	Overrides *ChainConfig `protobuf:"bytes,10,opt,name=overrides,proto3" json:"overrides,omitempty"`
	// enable_memory switches memory capture
	EnableMemory bool `protobuf:"varint,11,opt,name=enable_memory,json=enableMemory,proto3" json:"enable_memory,omitempty"`
	// enable_return_data switches the capture of return data
	EnableReturnData bool `protobuf:"varint,12,opt,name=enable_return_data,json=enableReturnData,proto3" json:"enable_return_data,omitempty"`
	// tracer_json_config configures the tracer using a JSON string
	TracerJsonConfig string `protobuf:"bytes,13,opt,name=tracer_json_config,json=tracerJsonConfig,proto3" json:"tracer_json_config,omitempty"`
}

func (x *TraceConfig) Reset() {
	*x = TraceConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cosmos_evm_vm_v1_evm_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraceConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraceConfig) ProtoMessage() {}

// Deprecated: Use TraceConfig.ProtoReflect.Descriptor instead.
func (*TraceConfig) Descriptor() ([]byte, []int) {
	return file_cosmos_evm_vm_v1_evm_proto_rawDescGZIP(), []int{9}
}

func (x *TraceConfig) GetTracer() string {
	if x != nil {
		return x.Tracer
	}
	return ""
}

func (x *TraceConfig) GetTimeout() string {
	if x != nil {
		return x.Timeout
	}
	return ""
}

func (x *TraceConfig) GetReexec() uint64 {
	if x != nil {
		return x.Reexec
	}
	return 0
}

func (x *TraceConfig) GetDisableStack() bool {
	if x != nil {
		return x.DisableStack
	}
	return false
}

func (x *TraceConfig) GetDisableStorage() bool {
	if x != nil {
		return x.DisableStorage
	}
	return false
}

func (x *TraceConfig) GetDebug() bool {
	if x != nil {
		return x.Debug
	}
	return false
}

func (x *TraceConfig) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *TraceConfig) GetOverrides() *ChainConfig {
	if x != nil {
		return x.Overrides
	}
	return nil
}

func (x *TraceConfig) GetEnableMemory() bool {
	if x != nil {
		return x.EnableMemory
	}
	return false
}

func (x *TraceConfig) GetEnableReturnData() bool {
	if x != nil {
		return x.EnableReturnData
	}
	return false
}

func (x *TraceConfig) GetTracerJsonConfig() string {
	if x != nil {
		return x.TracerJsonConfig
	}
	return ""
}

// Preinstall defines a contract that is preinstalled on-chain with a specific
// contract address and bytecode
type Preinstall struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name of the preinstall contract
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// address in hex format of the preinstall contract
	Address string `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	// code in hex format for the preinstall contract
	Code string `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *Preinstall) Reset() {
	*x = Preinstall{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cosmos_evm_vm_v1_evm_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Preinstall) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Preinstall) ProtoMessage() {}

// Deprecated: Use Preinstall.ProtoReflect.Descriptor instead.
func (*Preinstall) Descriptor() ([]byte, []int) {
	return file_cosmos_evm_vm_v1_evm_proto_rawDescGZIP(), []int{10}
}

func (x *Preinstall) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Preinstall) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *Preinstall) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

var File_cosmos_evm_vm_v1_evm_proto protoreflect.FileDescriptor

var file_cosmos_evm_vm_v1_evm_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x2f, 0x65, 0x76, 0x6d, 0x2f, 0x76, 0x6d, 0x2f,
	0x76, 0x31, 0x2f, 0x65, 0x76, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x63, 0x6f,
	0x73, 0x6d, 0x6f, 0x73, 0x2e, 0x65, 0x76, 0x6d, 0x2e, 0x76, 0x6d, 0x2e, 0x76, 0x31, 0x1a, 0x11,
	0x61, 0x6d, 0x69, 0x6e, 0x6f, 0x2f, 0x61, 0x6d, 0x69, 0x6e, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x14, 0x67, 0x6f, 0x67, 0x6f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x67,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb4, 0x03, 0x0a, 0x06, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x31, 0x0a, 0x09, 0x65, 0x76, 0x6d, 0x5f, 0x64, 0x65, 0x6e, 0x6f, 0x6d, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0xf2, 0xde, 0x1f, 0x10, 0x79, 0x61, 0x6d, 0x6c, 0x3a,
	0x22, 0x65, 0x76, 0x6d, 0x5f, 0x64, 0x65, 0x6e, 0x6f, 0x6d, 0x22, 0x52, 0x08, 0x65, 0x76, 0x6d,
	0x44, 0x65, 0x6e, 0x6f, 0x6d, 0x12, 0x41, 0x0a, 0x0a, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x65,
	0x69, 0x70, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x42, 0x22, 0xe2, 0xde, 0x1f, 0x09, 0x45,
	0x78, 0x74, 0x72, 0x61, 0x45, 0x49, 0x50, 0x73, 0xf2, 0xde, 0x1f, 0x11, 0x79, 0x61, 0x6d, 0x6c,
	0x3a, 0x22, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x65, 0x69, 0x70, 0x73, 0x22, 0x52, 0x09, 0x65,
	0x78, 0x74, 0x72, 0x61, 0x45, 0x69, 0x70, 0x73, 0x12, 0x32, 0x0a, 0x0c, 0x65, 0x76, 0x6d, 0x5f,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0f,
	0xe2, 0xde, 0x1f, 0x0b, 0x45, 0x56, 0x4d, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x52,
	0x0b, 0x65, 0x76, 0x6d, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x73, 0x12, 0x5d, 0x0a, 0x0e,
	0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x2e, 0x65, 0x76,
	0x6d, 0x2e, 0x76, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x42, 0x15, 0xc8, 0xde, 0x1f, 0x00, 0xe2, 0xde, 0x1f, 0x0d, 0x41,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x52, 0x0d, 0x61, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x12, 0x3a, 0x0a, 0x19, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x63, 0x5f, 0x70, 0x72, 0x65,
	0x63, 0x6f, 0x6d, 0x70, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x17,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x69, 0x63, 0x50, 0x72, 0x65, 0x63,
	0x6f, 0x6d, 0x70, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x68, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x12, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x3a, 0x1b, 0x8a, 0xe7, 0xb0, 0x2a, 0x16,
	0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x2f, 0x65, 0x76, 0x6d, 0x2f, 0x78, 0x2f, 0x76, 0x6d, 0x2f,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x4a, 0x04, 0x08, 0x02, 0x10, 0x03, 0x4a, 0x04, 0x08, 0x03,
	0x10, 0x04, 0x4a, 0x04, 0x08, 0x05, 0x10, 0x06, 0x4a, 0x04, 0x08, 0x06, 0x10, 0x07, 0x22, 0x91,
	0x01, 0x0a, 0x0d, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x12, 0x41, 0x0a, 0x06, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x2e, 0x65, 0x76, 0x6d, 0x2e, 0x76, 0x6d,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x42, 0x04, 0xc8, 0xde, 0x1f, 0x00, 0x52, 0x06, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x12, 0x3d, 0x0a, 0x04, 0x63, 0x61, 0x6c, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x2e, 0x65, 0x76, 0x6d, 0x2e, 0x76,
	0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x42, 0x04, 0xc8, 0xde, 0x1f, 0x00, 0x52, 0x04, 0x63, 0x61,
	0x6c, 0x6c, 0x22, 0xdd, 0x01, 0x0a, 0x11, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x63, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e,
	0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x2e, 0x65, 0x76, 0x6d, 0x2e, 0x76, 0x6d, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x42, 0x24, 0xe2, 0xde, 0x1f,
	0x0a, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0xf2, 0xde, 0x1f, 0x12, 0x79,
	0x61, 0x6d, 0x6c, 0x3a, 0x22, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x22, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x63, 0x0a,
	0x13, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x33, 0xe2, 0xde, 0x1f, 0x11,
	0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x4c, 0x69, 0x73,
	0x74, 0xf2, 0xde, 0x1f, 0x1a, 0x79, 0x61, 0x6d, 0x6c, 0x3a, 0x22, 0x61, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x52,
	0x11, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x4c, 0x69,
	0x73, 0x74, 0x22, 0xa8, 0x10, 0x0a, 0x0b, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x5c, 0x0a, 0x0f, 0x68, 0x6f, 0x6d, 0x65, 0x73, 0x74, 0x65, 0x61, 0x64, 0x5f,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x33, 0xda, 0xde, 0x1f,
	0x15, 0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x73, 0x64, 0x6b, 0x2e, 0x69, 0x6f, 0x2f, 0x6d, 0x61,
	0x74, 0x68, 0x2e, 0x49, 0x6e, 0x74, 0xf2, 0xde, 0x1f, 0x16, 0x79, 0x61, 0x6d, 0x6c, 0x3a, 0x22,
	0x68, 0x6f, 0x6d, 0x65, 0x73, 0x74, 0x65, 0x61, 0x64, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x22,
	0x52, 0x0e, 0x68, 0x6f, 0x6d, 0x65, 0x73, 0x74, 0x65, 0x61, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x12, 0x68, 0x0a, 0x0e, 0x64, 0x61, 0x6f, 0x5f, 0x66, 0x6f, 0x72, 0x6b, 0x5f, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x42, 0xda, 0xde, 0x1f, 0x15, 0x63, 0x6f,
	0x73, 0x6d, 0x6f, 0x73, 0x73, 0x64, 0x6b, 0x2e, 0x69, 0x6f, 0x2f, 0x6d, 0x61, 0x74, 0x68, 0x2e,
	0x49, 0x6e, 0x74, 0xe2, 0xde, 0x1f, 0x0c, 0x44, 0x41, 0x4f, 0x46, 0x6f, 0x72, 0x6b, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0xf2, 0xde, 0x1f, 0x15, 0x79, 0x61, 0x6d, 0x6c, 0x3a, 0x22, 0x64, 0x61, 0x6f,
	0x5f, 0x66, 0x6f, 0x72, 0x6b, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x22, 0x52, 0x0c, 0x64, 0x61,
	0x6f, 0x46, 0x6f, 0x72, 0x6b, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x57, 0x0a, 0x10, 0x64, 0x61,
	0x6f, 0x5f, 0x66, 0x6f, 0x72, 0x6b, 0x5f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x42, 0x2d, 0xe2, 0xde, 0x1f, 0x0e, 0x44, 0x41, 0x4f, 0x46, 0x6f, 0x72,
	0x6b, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0xf2, 0xde, 0x1f, 0x17, 0x79, 0x61, 0x6d, 0x6c,
	0x3a, 0x22, 0x64, 0x61, 0x6f, 0x5f, 0x66, 0x6f, 0x72, 0x6b, 0x5f, 0x73, 0x75, 0x70, 0x70, 0x6f,
	0x72, 0x74, 0x22, 0x52, 0x0e, 0x64, 0x61, 0x6f, 0x46, 0x6f, 0x72, 0x6b, 0x53, 0x75, 0x70, 0x70,
	0x6f, 0x72, 0x74, 0x12, 0x62, 0x0a, 0x0c, 0x65, 0x69, 0x70, 0x31, 0x35, 0x30, 0x5f, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x3f, 0xda, 0xde, 0x1f, 0x15, 0x63,
	0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x73, 0x64, 0x6b, 0x2e, 0x69, 0x6f, 0x2f, 0x6d, 0x61, 0x74, 0x68,
	0x2e, 0x49, 0x6e, 0x74, 0xe2, 0xde, 0x1f, 0x0b, 0x45, 0x49, 0x50, 0x31, 0x35, 0x30, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0xf2, 0xde, 0x1f, 0x13, 0x79, 0x61, 0x6d, 0x6c, 0x3a, 0x22, 0x65, 0x69, 0x70,
	0x31, 0x35, 0x30, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x22, 0x52, 0x0b, 0x65, 0x69, 0x70, 0x31,
	0x35, 0x30, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x62, 0x0a, 0x0c, 0x65, 0x69, 0x70, 0x31, 0x35,
	0x35, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x3f, 0xda,
	0xde, 0x1f, 0x15, 0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x73, 0x64, 0x6b, 0x2e, 0x69, 0x6f, 0x2f,
	0x6d, 0x61, 0x74, 0x68, 0x2e, 0x49, 0x6e, 0x74, 0xe2, 0xde, 0x1f, 0x0b, 0x45, 0x49, 0x50, 0x31,
	0x35, 0x35, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0xf2, 0xde, 0x1f, 0x13, 0x79, 0x61, 0x6d, 0x6c, 0x3a,
	0x22, 0x65, 0x69, 0x70, 0x31, 0x35, 0x35, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x22, 0x52, 0x0b,
	0x65, 0x69, 0x70, 0x31, 0x35, 0x35, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x62, 0x0a, 0x0c, 0x65,
	0x69, 0x70, 0x31, 0x35, 0x38, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x3f, 0xda, 0xde, 0x1f, 0x15, 0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x73, 0x64, 0x6b,
	0x2e, 0x69, 0x6f, 0x2f, 0x6d, 0x61, 0x74, 0x68, 0x2e, 0x49, 0x6e, 0x74, 0xe2, 0xde, 0x1f, 0x0b,
	0x45, 0x49, 0x50, 0x31, 0x35, 0x38, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0xf2, 0xde, 0x1f, 0x13, 0x79,
	0x61, 0x6d, 0x6c, 0x3a, 0x22, 0x65, 0x69, 0x70, 0x31, 0x35, 0x38, 0x5f, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x22, 0x52, 0x0b, 0x65, 0x69, 0x70, 0x31, 0x35, 0x38, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12,
	0x5c, 0x0a, 0x0f, 0x62, 0x79, 0x7a, 0x61, 0x6e, 0x74, 0x69, 0x75, 0x6d, 0x5f, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x33, 0xda, 0xde, 0x1f, 0x15, 0x63, 0x6f,
	0x73, 0x6d, 0x6f, 0x73, 0x73, 0x64, 0x6b, 0x2e, 0x69, 0x6f, 0x2f, 0x6d, 0x61, 0x74, 0x68, 0x2e,
	0x49, 0x6e, 0x74, 0xf2, 0xde, 0x1f, 0x16, 0x79, 0x61, 0x6d, 0x6c, 0x3a, 0x22, 0x62, 0x79, 0x7a,
	0x61, 0x6e, 0x74, 0x69, 0x75, 0x6d, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x22, 0x52, 0x0e, 0x62,
	0x79, 0x7a, 0x61, 0x6e, 0x74, 0x69, 0x75, 0x6d, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x6b, 0x0a,
	0x14, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x69, 0x6e, 0x6f, 0x70, 0x6c, 0x65, 0x5f,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x38, 0xda, 0xde, 0x1f,
	0x15, 0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x73, 0x64, 0x6b, 0x2e, 0x69, 0x6f, 0x2f, 0x6d, 0x61,
	0x74, 0x68, 0x2e, 0x49, 0x6e, 0x74, 0xf2, 0xde, 0x1f, 0x1b, 0x79, 0x61, 0x6d, 0x6c, 0x3a, 0x22,
	0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x69, 0x6e, 0x6f, 0x70, 0x6c, 0x65, 0x5f, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x22, 0x52, 0x13, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x69,
	0x6e, 0x6f, 0x70, 0x6c, 0x65, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x5f, 0x0a, 0x10, 0x70, 0x65,
	0x74, 0x65, 0x72, 0x73, 0x62, 0x75, 0x72, 0x67, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x34, 0xda, 0xde, 0x1f, 0x15, 0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73,
	0x73, 0x64, 0x6b, 0x2e, 0x69, 0x6f, 0x2f, 0x6d, 0x61, 0x74, 0x68, 0x2e, 0x49, 0x6e, 0x74, 0xf2,
	0xde, 0x1f, 0x17, 0x79, 0x61, 0x6d, 0x6c, 0x3a, 0x22, 0x70, 0x65, 0x74, 0x65, 0x72, 0x73, 0x62,
	0x75, 0x72, 0x67, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x22, 0x52, 0x0f, 0x70, 0x65, 0x74, 0x65,
	0x72, 0x73, 0x62, 0x75, 0x72, 0x67, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x59, 0x0a, 0x0e, 0x69,
	0x73, 0x74, 0x61, 0x6e, 0x62, 0x75, 0x6c, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x32, 0xda, 0xde, 0x1f, 0x15, 0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x73,
	0x64, 0x6b, 0x2e, 0x69, 0x6f, 0x2f, 0x6d, 0x61, 0x74, 0x68, 0x2e, 0x49, 0x6e, 0x74, 0xf2, 0xde,
	0x1f, 0x15, 0x79, 0x61, 0x6d, 0x6c, 0x3a, 0x22, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x62, 0x75, 0x6c,
	0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x22, 0x52, 0x0d, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x62, 0x75,
	0x6c, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x64, 0x0a, 0x12, 0x6d, 0x75, 0x69, 0x72, 0x5f, 0x67,
	0x6c, 0x61, 0x63, 0x69, 0x65, 0x72, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x36, 0xda, 0xde, 0x1f, 0x15, 0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x73, 0x64,
	0x6b, 0x2e, 0x69, 0x6f, 0x2f, 0x6d, 0x61, 0x74, 0x68, 0x2e, 0x49, 0x6e, 0x74, 0xf2, 0xde, 0x1f,
	0x19, 0x79, 0x61, 0x6d, 0x6c, 0x3a, 0x22, 0x6d, 0x75, 0x69, 0x72, 0x5f, 0x67, 0x6c, 0x61, 0x63,
	0x69, 0x65, 0x72, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x22, 0x52, 0x10, 0x6d, 0x75, 0x69, 0x72,
	0x47, 0x6c, 0x61, 0x63, 0x69, 0x65, 0x72, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x53, 0x0a, 0x0c,
	0x62, 0x65, 0x72, 0x6c, 0x69, 0x6e, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x30, 0xda, 0xde, 0x1f, 0x15, 0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x73, 0x64,
	0x6b, 0x2e, 0x69, 0x6f, 0x2f, 0x6d, 0x61, 0x74, 0x68, 0x2e, 0x49, 0x6e, 0x74, 0xf2, 0xde, 0x1f,
	0x13, 0x79, 0x61, 0x6d, 0x6c, 0x3a, 0x22, 0x62, 0x65, 0x72, 0x6c, 0x69, 0x6e, 0x5f, 0x62, 0x6c,
	0x6f, 0x63, 0x6b, 0x22, 0x52, 0x0b, 0x62, 0x65, 0x72, 0x6c, 0x69, 0x6e, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x12, 0x53, 0x0a, 0x0c, 0x6c, 0x6f, 0x6e, 0x64, 0x6f, 0x6e, 0x5f, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x42, 0x30, 0xda, 0xde, 0x1f, 0x15, 0x63, 0x6f, 0x73,
	0x6d, 0x6f, 0x73, 0x73, 0x64, 0x6b, 0x2e, 0x69, 0x6f, 0x2f, 0x6d, 0x61, 0x74, 0x68, 0x2e, 0x49,
	0x6e, 0x74, 0xf2, 0xde, 0x1f, 0x13, 0x79, 0x61, 0x6d, 0x6c, 0x3a, 0x22, 0x6c, 0x6f, 0x6e, 0x64,
	0x6f, 0x6e, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x22, 0x52, 0x0b, 0x6c, 0x6f, 0x6e, 0x64, 0x6f,
	0x6e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x67, 0x0a, 0x13, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x5f,
	0x67, 0x6c, 0x61, 0x63, 0x69, 0x65, 0x72, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x37, 0xda, 0xde, 0x1f, 0x15, 0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x73,
	0x64, 0x6b, 0x2e, 0x69, 0x6f, 0x2f, 0x6d, 0x61, 0x74, 0x68, 0x2e, 0x49, 0x6e, 0x74, 0xf2, 0xde,
	0x1f, 0x1a, 0x79, 0x61, 0x6d, 0x6c, 0x3a, 0x22, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x5f, 0x67, 0x6c,
	0x61, 0x63, 0x69, 0x65, 0x72, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x22, 0x52, 0x11, 0x61, 0x72,
	0x72, 0x6f, 0x77, 0x47, 0x6c, 0x61, 0x63, 0x69, 0x65, 0x72, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12,
	0x64, 0x0a, 0x12, 0x67, 0x72, 0x61, 0x79, 0x5f, 0x67, 0x6c, 0x61, 0x63, 0x69, 0x65, 0x72, 0x5f,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x42, 0x36, 0xda, 0xde, 0x1f,
	0x15, 0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x73, 0x64, 0x6b, 0x2e, 0x69, 0x6f, 0x2f, 0x6d, 0x61,
	0x74, 0x68, 0x2e, 0x49, 0x6e, 0x74, 0xf2, 0xde, 0x1f, 0x19, 0x79, 0x61, 0x6d, 0x6c, 0x3a, 0x22,
	0x67, 0x72, 0x61, 0x79, 0x5f, 0x67, 0x6c, 0x61, 0x63, 0x69, 0x65, 0x72, 0x5f, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x22, 0x52, 0x10, 0x67, 0x72, 0x61, 0x79, 0x47, 0x6c, 0x61, 0x63, 0x69, 0x65, 0x72,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x6a, 0x0a, 0x14, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x6e,
	0x65, 0x74, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x38, 0xda, 0xde, 0x1f, 0x15, 0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x73,
	0x64, 0x6b, 0x2e, 0x69, 0x6f, 0x2f, 0x6d, 0x61, 0x74, 0x68, 0x2e, 0x49, 0x6e, 0x74, 0xf2, 0xde,
	0x1f, 0x1b, 0x79, 0x61, 0x6d, 0x6c, 0x3a, 0x22, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f, 0x6e, 0x65,
	0x74, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x22, 0x52, 0x12, 0x6d,
	0x65, 0x72, 0x67, 0x65, 0x4e, 0x65, 0x74, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x18, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x07, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x64, 0x65, 0x6e, 0x6f, 0x6d, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x65, 0x6e,
	0x6f, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x73, 0x18, 0x1a,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x64, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x73, 0x12, 0x56,
	0x0a, 0x0d, 0x73, 0x68, 0x61, 0x6e, 0x67, 0x68, 0x61, 0x69, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x1b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x31, 0xda, 0xde, 0x1f, 0x15, 0x63, 0x6f, 0x73, 0x6d, 0x6f,
	0x73, 0x73, 0x64, 0x6b, 0x2e, 0x69, 0x6f, 0x2f, 0x6d, 0x61, 0x74, 0x68, 0x2e, 0x49, 0x6e, 0x74,
	0xf2, 0xde, 0x1f, 0x14, 0x79, 0x61, 0x6d, 0x6c, 0x3a, 0x22, 0x73, 0x68, 0x61, 0x6e, 0x67, 0x68,
	0x61, 0x69, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x52, 0x0c, 0x73, 0x68, 0x61, 0x6e, 0x67, 0x68,
	0x61, 0x69, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x50, 0x0a, 0x0b, 0x63, 0x61, 0x6e, 0x63, 0x75, 0x6e,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2f, 0xda, 0xde, 0x1f,
	0x15, 0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x73, 0x64, 0x6b, 0x2e, 0x69, 0x6f, 0x2f, 0x6d, 0x61,
	0x74, 0x68, 0x2e, 0x49, 0x6e, 0x74, 0xf2, 0xde, 0x1f, 0x12, 0x79, 0x61, 0x6d, 0x6c, 0x3a, 0x22,
	0x63, 0x61, 0x6e, 0x63, 0x75, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x52, 0x0a, 0x63, 0x61,
	0x6e, 0x63, 0x75, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x50, 0x0a, 0x0b, 0x70, 0x72, 0x61, 0x67,
	0x75, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2f, 0xda,
	0xde, 0x1f, 0x15, 0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x73, 0x64, 0x6b, 0x2e, 0x69, 0x6f, 0x2f,
	0x6d, 0x61, 0x74, 0x68, 0x2e, 0x49, 0x6e, 0x74, 0xf2, 0xde, 0x1f, 0x12, 0x79, 0x61, 0x6d, 0x6c,
	0x3a, 0x22, 0x70, 0x72, 0x61, 0x67, 0x75, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x52, 0x0a,
	0x70, 0x72, 0x61, 0x67, 0x75, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x50, 0x0a, 0x0b, 0x76, 0x65,
	0x72, 0x6b, 0x6c, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x2f, 0xda, 0xde, 0x1f, 0x15, 0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x73, 0x64, 0x6b, 0x2e, 0x69,
	0x6f, 0x2f, 0x6d, 0x61, 0x74, 0x68, 0x2e, 0x49, 0x6e, 0x74, 0xf2, 0xde, 0x1f, 0x12, 0x79, 0x61,
	0x6d, 0x6c, 0x3a, 0x22, 0x76, 0x65, 0x72, 0x6b, 0x6c, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x22,
	0x52, 0x0a, 0x76, 0x65, 0x72, 0x6b, 0x6c, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x4d, 0x0a, 0x0a,
	0x6f, 0x73, 0x61, 0x6b, 0x61, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x2e, 0xda, 0xde, 0x1f, 0x15, 0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x73, 0x64, 0x6b, 0x2e,
	0x69, 0x6f, 0x2f, 0x6d, 0x61, 0x74, 0x68, 0x2e, 0x49, 0x6e, 0x74, 0xf2, 0xde, 0x1f, 0x11, 0x79,
	0x61, 0x6d, 0x6c, 0x3a, 0x22, 0x6f, 0x73, 0x61, 0x6b, 0x61, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x22,
	0x52, 0x09, 0x6f, 0x73, 0x61, 0x6b, 0x61, 0x54, 0x69, 0x6d, 0x65, 0x4a, 0x04, 0x08, 0x05, 0x10,
	0x06, 0x4a, 0x04, 0x08, 0x16, 0x10, 0x17, 0x4a, 0x04, 0x08, 0x17, 0x10, 0x18, 0x22, 0x2f, 0x0a,
	0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x50,
	0x0a, 0x0f, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x6f, 0x67,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x29, 0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x2e, 0x65, 0x76, 0x6d,
	0x2e, 0x76, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x67, 0x52, 0x04, 0x6c, 0x6f, 0x67, 0x73,
	0x22, 0x87, 0x03, 0x0a, 0x03, 0x4c, 0x6f, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x06, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x32,
	0x0a, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x04, 0x42, 0x0f, 0xea, 0xde, 0x1f, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x2c, 0x0a, 0x07, 0x74, 0x78, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x13, 0xea, 0xde, 0x1f, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x73, 0x68, 0x52, 0x06, 0x74, 0x78, 0x48, 0x61, 0x73, 0x68,
	0x12, 0x2f, 0x0a, 0x08, 0x74, 0x78, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x04, 0x42, 0x14, 0xea, 0xde, 0x1f, 0x10, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x07, 0x74, 0x78, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x12, 0x2c, 0x0a, 0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0d, 0xea, 0xde, 0x1f, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x48, 0x61, 0x73, 0x68, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x61, 0x73, 0x68, 0x12,
	0x22, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x42, 0x0c,
	0xea, 0xde, 0x1f, 0x08, 0x6c, 0x6f, 0x67, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x05, 0x69, 0x6e,
	0x64, 0x65, 0x78, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x64, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x64, 0x12, 0x3b, 0x0a,
	0x0f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x04, 0x42, 0x12, 0xea, 0xde, 0x1f, 0x0e, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x90, 0x02, 0x0a, 0x08, 0x54,
	0x78, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x46, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x1b, 0xf2, 0xde, 0x1f, 0x17, 0x79, 0x61, 0x6d, 0x6c, 0x3a, 0x22, 0x63, 0x6f, 0x6e,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x52, 0x0f,
	0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x62, 0x6c, 0x6f, 0x6f, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05,
	0x62, 0x6c, 0x6f, 0x6f, 0x6d, 0x12, 0x57, 0x0a, 0x07, 0x74, 0x78, 0x5f, 0x6c, 0x6f, 0x67, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x2e,
	0x65, 0x76, 0x6d, 0x2e, 0x76, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x6f, 0x67, 0x73, 0x42, 0x1b, 0xc8, 0xde, 0x1f, 0x00, 0xf2,
	0xde, 0x1f, 0x0e, 0x79, 0x61, 0x6d, 0x6c, 0x3a, 0x22, 0x74, 0x78, 0x5f, 0x6c, 0x6f, 0x67, 0x73,
	0x22, 0xa8, 0xe7, 0xb0, 0x2a, 0x01, 0x52, 0x06, 0x74, 0x78, 0x4c, 0x6f, 0x67, 0x73, 0x12, 0x10,
	0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03, 0x72, 0x65, 0x74,
	0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x65, 0x72, 0x74, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x76, 0x65, 0x72, 0x74, 0x65, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x67, 0x61, 0x73, 0x5f, 0x75, 0x73, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07,
	0x67, 0x61, 0x73, 0x55, 0x73, 0x65, 0x64, 0x3a, 0x04, 0x88, 0xa0, 0x1f, 0x00, 0x22, 0x61, 0x0a,
	0x0b, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x75, 0x70, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x32, 0x0a, 0x0c, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67,
	0x65, 0x5f, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0f, 0xea, 0xde,
	0x1f, 0x0b, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x4b, 0x65, 0x79, 0x73, 0x52, 0x0b, 0x73,
	0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x4b, 0x65, 0x79, 0x73, 0x3a, 0x04, 0x88, 0xa0, 0x1f, 0x00,
	0x22, 0xa0, 0x04, 0x0a, 0x0b, 0x54, 0x72, 0x61, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x16, 0x0a, 0x06, 0x74, 0x72, 0x61, 0x63, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x74, 0x72, 0x61, 0x63, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x65, 0x78, 0x65, 0x63, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x06, 0x72, 0x65, 0x65, 0x78, 0x65, 0x63, 0x12, 0x35, 0x0a, 0x0d, 0x64, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x08, 0x42, 0x10, 0xea, 0xde, 0x1f, 0x0c, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74,
	0x61, 0x63, 0x6b, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x63,
	0x6b, 0x12, 0x3b, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x42, 0x12, 0xea, 0xde, 0x1f, 0x0e,
	0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x52, 0x0e,
	0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x64, 0x65, 0x62, 0x75, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x64,
	0x65, 0x62, 0x75, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x3b, 0x0a, 0x09, 0x6f, 0x76,
	0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x2e, 0x65, 0x76, 0x6d, 0x2e, 0x76, 0x6d, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x09, 0x6f, 0x76,
	0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x12, 0x35, 0x0a, 0x0d, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x5f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x42, 0x10,
	0xea, 0xde, 0x1f, 0x0c, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79,
	0x52, 0x0c, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x42,
	0x0a, 0x12, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x42, 0x14, 0xea, 0xde, 0x1f, 0x10,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x10, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x3e, 0x0a, 0x12, 0x74, 0x72, 0x61, 0x63, 0x65, 0x72, 0x5f, 0x6a, 0x73, 0x6f,
	0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x10,
	0xea, 0xde, 0x1f, 0x0c, 0x74, 0x72, 0x61, 0x63, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x10, 0x74, 0x72, 0x61, 0x63, 0x65, 0x72, 0x4a, 0x73, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x4a, 0x04, 0x08, 0x04, 0x10, 0x05, 0x4a, 0x04, 0x08, 0x07, 0x10, 0x08, 0x52, 0x0e,
	0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x52, 0x13,
	0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x22, 0x4e, 0x0a, 0x0a, 0x50, 0x72, 0x65, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x2a, 0xc0, 0x01, 0x0a, 0x0a, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x3c, 0x0a, 0x1a, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x50, 0x45, 0x52, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x4c, 0x45, 0x53, 0x53,
	0x10, 0x00, 0x1a, 0x1c, 0x8a, 0x9d, 0x20, 0x18, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x79,
	0x70, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x6c, 0x65, 0x73, 0x73,
	0x12, 0x34, 0x0a, 0x16, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x52, 0x45, 0x53, 0x54, 0x52, 0x49, 0x43, 0x54, 0x45, 0x44, 0x10, 0x01, 0x1a, 0x18, 0x8a, 0x9d,
	0x20, 0x14, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x74,
	0x72, 0x69, 0x63, 0x74, 0x65, 0x64, 0x12, 0x38, 0x0a, 0x18, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e,
	0x45, 0x44, 0x10, 0x02, 0x1a, 0x1a, 0x8a, 0x9d, 0x20, 0x16, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x65, 0x64,
	0x1a, 0x04, 0x88, 0xa3, 0x1e, 0x00, 0x42, 0xab, 0x01, 0x0a, 0x14, 0x63, 0x6f, 0x6d, 0x2e, 0x63,
	0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x2e, 0x65, 0x76, 0x6d, 0x2e, 0x76, 0x6d, 0x2e, 0x76, 0x31, 0x42,
	0x08, 0x45, 0x76, 0x6d, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x26, 0x63, 0x6f, 0x73,
	0x6d, 0x6f, 0x73, 0x73, 0x64, 0x6b, 0x2e, 0x69, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f,
	0x73, 0x6d, 0x6f, 0x73, 0x2f, 0x65, 0x76, 0x6d, 0x2f, 0x76, 0x6d, 0x2f, 0x76, 0x31, 0x3b, 0x76,
	0x6d, 0x76, 0x31, 0xa2, 0x02, 0x03, 0x43, 0x45, 0x56, 0xaa, 0x02, 0x10, 0x43, 0x6f, 0x73, 0x6d,
	0x6f, 0x73, 0x2e, 0x45, 0x76, 0x6d, 0x2e, 0x56, 0x6d, 0x2e, 0x56, 0x31, 0xca, 0x02, 0x10, 0x43,
	0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x5c, 0x45, 0x76, 0x6d, 0x5c, 0x56, 0x6d, 0x5c, 0x56, 0x31, 0xe2,
	0x02, 0x1c, 0x43, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x5c, 0x45, 0x76, 0x6d, 0x5c, 0x56, 0x6d, 0x5c,
	0x56, 0x31, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0xea, 0x02,
	0x13, 0x43, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x3a, 0x3a, 0x45, 0x76, 0x6d, 0x3a, 0x3a, 0x56, 0x6d,
	0x3a, 0x3a, 0x56, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_cosmos_evm_vm_v1_evm_proto_rawDescOnce sync.Once
	file_cosmos_evm_vm_v1_evm_proto_rawDescData = file_cosmos_evm_vm_v1_evm_proto_rawDesc
)

func file_cosmos_evm_vm_v1_evm_proto_rawDescGZIP() []byte {
	file_cosmos_evm_vm_v1_evm_proto_rawDescOnce.Do(func() {
		file_cosmos_evm_vm_v1_evm_proto_rawDescData = protoimpl.X.CompressGZIP(file_cosmos_evm_vm_v1_evm_proto_rawDescData)
	})
	return file_cosmos_evm_vm_v1_evm_proto_rawDescData
}

var file_cosmos_evm_vm_v1_evm_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_cosmos_evm_vm_v1_evm_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_cosmos_evm_vm_v1_evm_proto_goTypes = []interface{}{
	(AccessType)(0),           // 0: cosmos.evm.vm.v1.AccessType
	(*Params)(nil),            // 1: cosmos.evm.vm.v1.Params
	(*AccessControl)(nil),     // 2: cosmos.evm.vm.v1.AccessControl
	(*AccessControlType)(nil), // 3: cosmos.evm.vm.v1.AccessControlType
	(*ChainConfig)(nil),       // 4: cosmos.evm.vm.v1.ChainConfig
	(*State)(nil),             // 5: cosmos.evm.vm.v1.State
	(*TransactionLogs)(nil),   // 6: cosmos.evm.vm.v1.TransactionLogs
	(*Log)(nil),               // 7: cosmos.evm.vm.v1.Log
	(*TxResult)(nil),          // 8: cosmos.evm.vm.v1.TxResult
	(*AccessTuple)(nil),       // 9: cosmos.evm.vm.v1.AccessTuple
	(*TraceConfig)(nil),       // 10: cosmos.evm.vm.v1.TraceConfig
	(*Preinstall)(nil),        // 11: cosmos.evm.vm.v1.Preinstall
}
var file_cosmos_evm_vm_v1_evm_proto_depIdxs = []int32{
	2, // 0: cosmos.evm.vm.v1.Params.access_control:type_name -> cosmos.evm.vm.v1.AccessControl
	3, // 1: cosmos.evm.vm.v1.AccessControl.create:type_name -> cosmos.evm.vm.v1.AccessControlType
	3, // 2: cosmos.evm.vm.v1.AccessControl.call:type_name -> cosmos.evm.vm.v1.AccessControlType
	0, // 3: cosmos.evm.vm.v1.AccessControlType.access_type:type_name -> cosmos.evm.vm.v1.AccessType
	7, // 4: cosmos.evm.vm.v1.TransactionLogs.logs:type_name -> cosmos.evm.vm.v1.Log
	6, // 5: cosmos.evm.vm.v1.TxResult.tx_logs:type_name -> cosmos.evm.vm.v1.TransactionLogs
	4, // 6: cosmos.evm.vm.v1.TraceConfig.overrides:type_name -> cosmos.evm.vm.v1.ChainConfig
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_cosmos_evm_vm_v1_evm_proto_init() }
func file_cosmos_evm_vm_v1_evm_proto_init() {
	if File_cosmos_evm_vm_v1_evm_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_cosmos_evm_vm_v1_evm_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Params); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cosmos_evm_vm_v1_evm_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccessControl); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cosmos_evm_vm_v1_evm_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccessControlType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cosmos_evm_vm_v1_evm_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChainConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cosmos_evm_vm_v1_evm_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*State); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cosmos_evm_vm_v1_evm_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransactionLogs); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cosmos_evm_vm_v1_evm_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Log); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cosmos_evm_vm_v1_evm_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TxResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cosmos_evm_vm_v1_evm_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccessTuple); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cosmos_evm_vm_v1_evm_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraceConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_cosmos_evm_vm_v1_evm_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Preinstall); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_cosmos_evm_vm_v1_evm_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_cosmos_evm_vm_v1_evm_proto_goTypes,
		DependencyIndexes: file_cosmos_evm_vm_v1_evm_proto_depIdxs,
		EnumInfos:         file_cosmos_evm_vm_v1_evm_proto_enumTypes,
		MessageInfos:      file_cosmos_evm_vm_v1_evm_proto_msgTypes,
	}.Build()
	File_cosmos_evm_vm_v1_evm_proto = out.File
	file_cosmos_evm_vm_v1_evm_proto_rawDesc = nil
	file_cosmos_evm_vm_v1_evm_proto_goTypes = nil
	file_cosmos_evm_vm_v1_evm_proto_depIdxs = nil
}
