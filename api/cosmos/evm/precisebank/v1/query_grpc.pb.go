// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             (unknown)
// source: cosmos/evm/precisebank/v1/query.proto

package precisebankv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Query_Remainder_FullMethodName         = "/cosmos.evm.precisebank.v1.Query/Remainder"
	Query_FractionalBalance_FullMethodName = "/cosmos.evm.precisebank.v1.Query/FractionalBalance"
)

// QueryClient is the client API for Query service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type QueryClient interface {
	// Remainder returns the amount backed by the reserve, but not yet owned by
	// any account, i.e. not in circulation.
	Remainder(ctx context.Context, in *QueryRemainderRequest, opts ...grpc.CallOption) (*QueryRemainderResponse, error)
	// FractionalBalance returns only the fractional balance of an address. This
	// does not include any integer balance.
	FractionalBalance(ctx context.Context, in *QueryFractionalBalanceRequest, opts ...grpc.CallOption) (*QueryFractionalBalanceResponse, error)
}

type queryClient struct {
	cc grpc.ClientConnInterface
}

func NewQueryClient(cc grpc.ClientConnInterface) QueryClient {
	return &queryClient{cc}
}

func (c *queryClient) Remainder(ctx context.Context, in *QueryRemainderRequest, opts ...grpc.CallOption) (*QueryRemainderResponse, error) {
	out := new(QueryRemainderResponse)
	err := c.cc.Invoke(ctx, Query_Remainder_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *queryClient) FractionalBalance(ctx context.Context, in *QueryFractionalBalanceRequest, opts ...grpc.CallOption) (*QueryFractionalBalanceResponse, error) {
	out := new(QueryFractionalBalanceResponse)
	err := c.cc.Invoke(ctx, Query_FractionalBalance_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// QueryServer is the server API for Query service.
// All implementations must embed UnimplementedQueryServer
// for forward compatibility
type QueryServer interface {
	// Remainder returns the amount backed by the reserve, but not yet owned by
	// any account, i.e. not in circulation.
	Remainder(context.Context, *QueryRemainderRequest) (*QueryRemainderResponse, error)
	// FractionalBalance returns only the fractional balance of an address. This
	// does not include any integer balance.
	FractionalBalance(context.Context, *QueryFractionalBalanceRequest) (*QueryFractionalBalanceResponse, error)
	mustEmbedUnimplementedQueryServer()
}

// UnimplementedQueryServer must be embedded to have forward compatible implementations.
type UnimplementedQueryServer struct {
}

func (UnimplementedQueryServer) Remainder(context.Context, *QueryRemainderRequest) (*QueryRemainderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Remainder not implemented")
}
func (UnimplementedQueryServer) FractionalBalance(context.Context, *QueryFractionalBalanceRequest) (*QueryFractionalBalanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FractionalBalance not implemented")
}
func (UnimplementedQueryServer) mustEmbedUnimplementedQueryServer() {}

// UnsafeQueryServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to QueryServer will
// result in compilation errors.
type UnsafeQueryServer interface {
	mustEmbedUnimplementedQueryServer()
}

func RegisterQueryServer(s grpc.ServiceRegistrar, srv QueryServer) {
	s.RegisterService(&Query_ServiceDesc, srv)
}

func _Query_Remainder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryRemainderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QueryServer).Remainder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Query_Remainder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QueryServer).Remainder(ctx, req.(*QueryRemainderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Query_FractionalBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryFractionalBalanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QueryServer).FractionalBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Query_FractionalBalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QueryServer).FractionalBalance(ctx, req.(*QueryFractionalBalanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Query_ServiceDesc is the grpc.ServiceDesc for Query service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Query_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "cosmos.evm.precisebank.v1.Query",
	HandlerType: (*QueryServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Remainder",
			Handler:    _Query_Remainder_Handler,
		},
		{
			MethodName: "FractionalBalance",
			Handler:    _Query_FractionalBalance_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "cosmos/evm/precisebank/v1/query.proto",
}
