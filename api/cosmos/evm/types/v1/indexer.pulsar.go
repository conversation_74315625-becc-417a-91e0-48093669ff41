// Code generated by protoc-gen-go-pulsar. DO NOT EDIT.
package typesv1

import (
	fmt "fmt"
	runtime "github.com/cosmos/cosmos-proto/runtime"
	_ "github.com/cosmos/gogoproto/gogoproto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoiface "google.golang.org/protobuf/runtime/protoiface"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	io "io"
	reflect "reflect"
	sync "sync"
)

var (
	md_TxResult                     protoreflect.MessageDescriptor
	fd_TxResult_height              protoreflect.FieldDescriptor
	fd_TxResult_tx_index            protoreflect.FieldDescriptor
	fd_TxResult_msg_index           protoreflect.FieldDescriptor
	fd_TxResult_eth_tx_index        protoreflect.FieldDescriptor
	fd_TxResult_failed              protoreflect.FieldDescriptor
	fd_TxResult_gas_used            protoreflect.FieldDescriptor
	fd_TxResult_cumulative_gas_used protoreflect.FieldDescriptor
)

func init() {
	file_cosmos_evm_types_v1_indexer_proto_init()
	md_TxResult = File_cosmos_evm_types_v1_indexer_proto.Messages().ByName("TxResult")
	fd_TxResult_height = md_TxResult.Fields().ByName("height")
	fd_TxResult_tx_index = md_TxResult.Fields().ByName("tx_index")
	fd_TxResult_msg_index = md_TxResult.Fields().ByName("msg_index")
	fd_TxResult_eth_tx_index = md_TxResult.Fields().ByName("eth_tx_index")
	fd_TxResult_failed = md_TxResult.Fields().ByName("failed")
	fd_TxResult_gas_used = md_TxResult.Fields().ByName("gas_used")
	fd_TxResult_cumulative_gas_used = md_TxResult.Fields().ByName("cumulative_gas_used")
}

var _ protoreflect.Message = (*fastReflection_TxResult)(nil)

type fastReflection_TxResult TxResult

func (x *TxResult) ProtoReflect() protoreflect.Message {
	return (*fastReflection_TxResult)(x)
}

func (x *TxResult) slowProtoReflect() protoreflect.Message {
	mi := &file_cosmos_evm_types_v1_indexer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

var _fastReflection_TxResult_messageType fastReflection_TxResult_messageType
var _ protoreflect.MessageType = fastReflection_TxResult_messageType{}

type fastReflection_TxResult_messageType struct{}

func (x fastReflection_TxResult_messageType) Zero() protoreflect.Message {
	return (*fastReflection_TxResult)(nil)
}
func (x fastReflection_TxResult_messageType) New() protoreflect.Message {
	return new(fastReflection_TxResult)
}
func (x fastReflection_TxResult_messageType) Descriptor() protoreflect.MessageDescriptor {
	return md_TxResult
}

// Descriptor returns message descriptor, which contains only the protobuf
// type information for the message.
func (x *fastReflection_TxResult) Descriptor() protoreflect.MessageDescriptor {
	return md_TxResult
}

// Type returns the message type, which encapsulates both Go and protobuf
// type information. If the Go type information is not needed,
// it is recommended that the message descriptor be used instead.
func (x *fastReflection_TxResult) Type() protoreflect.MessageType {
	return _fastReflection_TxResult_messageType
}

// New returns a newly allocated and mutable empty message.
func (x *fastReflection_TxResult) New() protoreflect.Message {
	return new(fastReflection_TxResult)
}

// Interface unwraps the message reflection interface and
// returns the underlying ProtoMessage interface.
func (x *fastReflection_TxResult) Interface() protoreflect.ProtoMessage {
	return (*TxResult)(x)
}

// Range iterates over every populated field in an undefined order,
// calling f for each field descriptor and value encountered.
// Range returns immediately if f returns false.
// While iterating, mutating operations may only be performed
// on the current field descriptor.
func (x *fastReflection_TxResult) Range(f func(protoreflect.FieldDescriptor, protoreflect.Value) bool) {
	if x.Height != int64(0) {
		value := protoreflect.ValueOfInt64(x.Height)
		if !f(fd_TxResult_height, value) {
			return
		}
	}
	if x.TxIndex != uint32(0) {
		value := protoreflect.ValueOfUint32(x.TxIndex)
		if !f(fd_TxResult_tx_index, value) {
			return
		}
	}
	if x.MsgIndex != uint32(0) {
		value := protoreflect.ValueOfUint32(x.MsgIndex)
		if !f(fd_TxResult_msg_index, value) {
			return
		}
	}
	if x.EthTxIndex != int32(0) {
		value := protoreflect.ValueOfInt32(x.EthTxIndex)
		if !f(fd_TxResult_eth_tx_index, value) {
			return
		}
	}
	if x.Failed != false {
		value := protoreflect.ValueOfBool(x.Failed)
		if !f(fd_TxResult_failed, value) {
			return
		}
	}
	if x.GasUsed != uint64(0) {
		value := protoreflect.ValueOfUint64(x.GasUsed)
		if !f(fd_TxResult_gas_used, value) {
			return
		}
	}
	if x.CumulativeGasUsed != uint64(0) {
		value := protoreflect.ValueOfUint64(x.CumulativeGasUsed)
		if !f(fd_TxResult_cumulative_gas_used, value) {
			return
		}
	}
}

// Has reports whether a field is populated.
//
// Some fields have the property of nullability where it is possible to
// distinguish between the default value of a field and whether the field
// was explicitly populated with the default value. Singular message fields,
// member fields of a oneof, and proto2 scalar fields are nullable. Such
// fields are populated only if explicitly set.
//
// In other cases (aside from the nullable cases above),
// a proto3 scalar field is populated if it contains a non-zero value, and
// a repeated field is populated if it is non-empty.
func (x *fastReflection_TxResult) Has(fd protoreflect.FieldDescriptor) bool {
	switch fd.FullName() {
	case "cosmos.evm.types.v1.TxResult.height":
		return x.Height != int64(0)
	case "cosmos.evm.types.v1.TxResult.tx_index":
		return x.TxIndex != uint32(0)
	case "cosmos.evm.types.v1.TxResult.msg_index":
		return x.MsgIndex != uint32(0)
	case "cosmos.evm.types.v1.TxResult.eth_tx_index":
		return x.EthTxIndex != int32(0)
	case "cosmos.evm.types.v1.TxResult.failed":
		return x.Failed != false
	case "cosmos.evm.types.v1.TxResult.gas_used":
		return x.GasUsed != uint64(0)
	case "cosmos.evm.types.v1.TxResult.cumulative_gas_used":
		return x.CumulativeGasUsed != uint64(0)
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.types.v1.TxResult"))
		}
		panic(fmt.Errorf("message cosmos.evm.types.v1.TxResult does not contain field %s", fd.FullName()))
	}
}

// Clear clears the field such that a subsequent Has call reports false.
//
// Clearing an extension field clears both the extension type and value
// associated with the given field number.
//
// Clear is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_TxResult) Clear(fd protoreflect.FieldDescriptor) {
	switch fd.FullName() {
	case "cosmos.evm.types.v1.TxResult.height":
		x.Height = int64(0)
	case "cosmos.evm.types.v1.TxResult.tx_index":
		x.TxIndex = uint32(0)
	case "cosmos.evm.types.v1.TxResult.msg_index":
		x.MsgIndex = uint32(0)
	case "cosmos.evm.types.v1.TxResult.eth_tx_index":
		x.EthTxIndex = int32(0)
	case "cosmos.evm.types.v1.TxResult.failed":
		x.Failed = false
	case "cosmos.evm.types.v1.TxResult.gas_used":
		x.GasUsed = uint64(0)
	case "cosmos.evm.types.v1.TxResult.cumulative_gas_used":
		x.CumulativeGasUsed = uint64(0)
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.types.v1.TxResult"))
		}
		panic(fmt.Errorf("message cosmos.evm.types.v1.TxResult does not contain field %s", fd.FullName()))
	}
}

// Get retrieves the value for a field.
//
// For unpopulated scalars, it returns the default value, where
// the default value of a bytes scalar is guaranteed to be a copy.
// For unpopulated composite types, it returns an empty, read-only view
// of the value; to obtain a mutable reference, use Mutable.
func (x *fastReflection_TxResult) Get(descriptor protoreflect.FieldDescriptor) protoreflect.Value {
	switch descriptor.FullName() {
	case "cosmos.evm.types.v1.TxResult.height":
		value := x.Height
		return protoreflect.ValueOfInt64(value)
	case "cosmos.evm.types.v1.TxResult.tx_index":
		value := x.TxIndex
		return protoreflect.ValueOfUint32(value)
	case "cosmos.evm.types.v1.TxResult.msg_index":
		value := x.MsgIndex
		return protoreflect.ValueOfUint32(value)
	case "cosmos.evm.types.v1.TxResult.eth_tx_index":
		value := x.EthTxIndex
		return protoreflect.ValueOfInt32(value)
	case "cosmos.evm.types.v1.TxResult.failed":
		value := x.Failed
		return protoreflect.ValueOfBool(value)
	case "cosmos.evm.types.v1.TxResult.gas_used":
		value := x.GasUsed
		return protoreflect.ValueOfUint64(value)
	case "cosmos.evm.types.v1.TxResult.cumulative_gas_used":
		value := x.CumulativeGasUsed
		return protoreflect.ValueOfUint64(value)
	default:
		if descriptor.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.types.v1.TxResult"))
		}
		panic(fmt.Errorf("message cosmos.evm.types.v1.TxResult does not contain field %s", descriptor.FullName()))
	}
}

// Set stores the value for a field.
//
// For a field belonging to a oneof, it implicitly clears any other field
// that may be currently set within the same oneof.
// For extension fields, it implicitly stores the provided ExtensionType.
// When setting a composite type, it is unspecified whether the stored value
// aliases the source's memory in any way. If the composite value is an
// empty, read-only value, then it panics.
//
// Set is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_TxResult) Set(fd protoreflect.FieldDescriptor, value protoreflect.Value) {
	switch fd.FullName() {
	case "cosmos.evm.types.v1.TxResult.height":
		x.Height = value.Int()
	case "cosmos.evm.types.v1.TxResult.tx_index":
		x.TxIndex = uint32(value.Uint())
	case "cosmos.evm.types.v1.TxResult.msg_index":
		x.MsgIndex = uint32(value.Uint())
	case "cosmos.evm.types.v1.TxResult.eth_tx_index":
		x.EthTxIndex = int32(value.Int())
	case "cosmos.evm.types.v1.TxResult.failed":
		x.Failed = value.Bool()
	case "cosmos.evm.types.v1.TxResult.gas_used":
		x.GasUsed = value.Uint()
	case "cosmos.evm.types.v1.TxResult.cumulative_gas_used":
		x.CumulativeGasUsed = value.Uint()
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.types.v1.TxResult"))
		}
		panic(fmt.Errorf("message cosmos.evm.types.v1.TxResult does not contain field %s", fd.FullName()))
	}
}

// Mutable returns a mutable reference to a composite type.
//
// If the field is unpopulated, it may allocate a composite value.
// For a field belonging to a oneof, it implicitly clears any other field
// that may be currently set within the same oneof.
// For extension fields, it implicitly stores the provided ExtensionType
// if not already stored.
// It panics if the field does not contain a composite type.
//
// Mutable is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_TxResult) Mutable(fd protoreflect.FieldDescriptor) protoreflect.Value {
	switch fd.FullName() {
	case "cosmos.evm.types.v1.TxResult.height":
		panic(fmt.Errorf("field height of message cosmos.evm.types.v1.TxResult is not mutable"))
	case "cosmos.evm.types.v1.TxResult.tx_index":
		panic(fmt.Errorf("field tx_index of message cosmos.evm.types.v1.TxResult is not mutable"))
	case "cosmos.evm.types.v1.TxResult.msg_index":
		panic(fmt.Errorf("field msg_index of message cosmos.evm.types.v1.TxResult is not mutable"))
	case "cosmos.evm.types.v1.TxResult.eth_tx_index":
		panic(fmt.Errorf("field eth_tx_index of message cosmos.evm.types.v1.TxResult is not mutable"))
	case "cosmos.evm.types.v1.TxResult.failed":
		panic(fmt.Errorf("field failed of message cosmos.evm.types.v1.TxResult is not mutable"))
	case "cosmos.evm.types.v1.TxResult.gas_used":
		panic(fmt.Errorf("field gas_used of message cosmos.evm.types.v1.TxResult is not mutable"))
	case "cosmos.evm.types.v1.TxResult.cumulative_gas_used":
		panic(fmt.Errorf("field cumulative_gas_used of message cosmos.evm.types.v1.TxResult is not mutable"))
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.types.v1.TxResult"))
		}
		panic(fmt.Errorf("message cosmos.evm.types.v1.TxResult does not contain field %s", fd.FullName()))
	}
}

// NewField returns a new value that is assignable to the field
// for the given descriptor. For scalars, this returns the default value.
// For lists, maps, and messages, this returns a new, empty, mutable value.
func (x *fastReflection_TxResult) NewField(fd protoreflect.FieldDescriptor) protoreflect.Value {
	switch fd.FullName() {
	case "cosmos.evm.types.v1.TxResult.height":
		return protoreflect.ValueOfInt64(int64(0))
	case "cosmos.evm.types.v1.TxResult.tx_index":
		return protoreflect.ValueOfUint32(uint32(0))
	case "cosmos.evm.types.v1.TxResult.msg_index":
		return protoreflect.ValueOfUint32(uint32(0))
	case "cosmos.evm.types.v1.TxResult.eth_tx_index":
		return protoreflect.ValueOfInt32(int32(0))
	case "cosmos.evm.types.v1.TxResult.failed":
		return protoreflect.ValueOfBool(false)
	case "cosmos.evm.types.v1.TxResult.gas_used":
		return protoreflect.ValueOfUint64(uint64(0))
	case "cosmos.evm.types.v1.TxResult.cumulative_gas_used":
		return protoreflect.ValueOfUint64(uint64(0))
	default:
		if fd.IsExtension() {
			panic(fmt.Errorf("proto3 declared messages do not support extensions: cosmos.evm.types.v1.TxResult"))
		}
		panic(fmt.Errorf("message cosmos.evm.types.v1.TxResult does not contain field %s", fd.FullName()))
	}
}

// WhichOneof reports which field within the oneof is populated,
// returning nil if none are populated.
// It panics if the oneof descriptor does not belong to this message.
func (x *fastReflection_TxResult) WhichOneof(d protoreflect.OneofDescriptor) protoreflect.FieldDescriptor {
	switch d.FullName() {
	default:
		panic(fmt.Errorf("%s is not a oneof field in cosmos.evm.types.v1.TxResult", d.FullName()))
	}
	panic("unreachable")
}

// GetUnknown retrieves the entire list of unknown fields.
// The caller may only mutate the contents of the RawFields
// if the mutated bytes are stored back into the message with SetUnknown.
func (x *fastReflection_TxResult) GetUnknown() protoreflect.RawFields {
	return x.unknownFields
}

// SetUnknown stores an entire list of unknown fields.
// The raw fields must be syntactically valid according to the wire format.
// An implementation may panic if this is not the case.
// Once stored, the caller must not mutate the content of the RawFields.
// An empty RawFields may be passed to clear the fields.
//
// SetUnknown is a mutating operation and unsafe for concurrent use.
func (x *fastReflection_TxResult) SetUnknown(fields protoreflect.RawFields) {
	x.unknownFields = fields
}

// IsValid reports whether the message is valid.
//
// An invalid message is an empty, read-only value.
//
// An invalid message often corresponds to a nil pointer of the concrete
// message type, but the details are implementation dependent.
// Validity is not part of the protobuf data model, and may not
// be preserved in marshaling or other operations.
func (x *fastReflection_TxResult) IsValid() bool {
	return x != nil
}

// ProtoMethods returns optional fastReflectionFeature-path implementations of various operations.
// This method may return nil.
//
// The returned methods type is identical to
// "google.golang.org/protobuf/runtime/protoiface".Methods.
// Consult the protoiface package documentation for details.
func (x *fastReflection_TxResult) ProtoMethods() *protoiface.Methods {
	size := func(input protoiface.SizeInput) protoiface.SizeOutput {
		x := input.Message.Interface().(*TxResult)
		if x == nil {
			return protoiface.SizeOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Size:              0,
			}
		}
		options := runtime.SizeInputToOptions(input)
		_ = options
		var n int
		var l int
		_ = l
		if x.Height != 0 {
			n += 1 + runtime.Sov(uint64(x.Height))
		}
		if x.TxIndex != 0 {
			n += 1 + runtime.Sov(uint64(x.TxIndex))
		}
		if x.MsgIndex != 0 {
			n += 1 + runtime.Sov(uint64(x.MsgIndex))
		}
		if x.EthTxIndex != 0 {
			n += 1 + runtime.Sov(uint64(x.EthTxIndex))
		}
		if x.Failed {
			n += 2
		}
		if x.GasUsed != 0 {
			n += 1 + runtime.Sov(uint64(x.GasUsed))
		}
		if x.CumulativeGasUsed != 0 {
			n += 1 + runtime.Sov(uint64(x.CumulativeGasUsed))
		}
		if x.unknownFields != nil {
			n += len(x.unknownFields)
		}
		return protoiface.SizeOutput{
			NoUnkeyedLiterals: input.NoUnkeyedLiterals,
			Size:              n,
		}
	}

	marshal := func(input protoiface.MarshalInput) (protoiface.MarshalOutput, error) {
		x := input.Message.Interface().(*TxResult)
		if x == nil {
			return protoiface.MarshalOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Buf:               input.Buf,
			}, nil
		}
		options := runtime.MarshalInputToOptions(input)
		_ = options
		size := options.Size(x)
		dAtA := make([]byte, size)
		i := len(dAtA)
		_ = i
		var l int
		_ = l
		if x.unknownFields != nil {
			i -= len(x.unknownFields)
			copy(dAtA[i:], x.unknownFields)
		}
		if x.CumulativeGasUsed != 0 {
			i = runtime.EncodeVarint(dAtA, i, uint64(x.CumulativeGasUsed))
			i--
			dAtA[i] = 0x38
		}
		if x.GasUsed != 0 {
			i = runtime.EncodeVarint(dAtA, i, uint64(x.GasUsed))
			i--
			dAtA[i] = 0x30
		}
		if x.Failed {
			i--
			if x.Failed {
				dAtA[i] = 1
			} else {
				dAtA[i] = 0
			}
			i--
			dAtA[i] = 0x28
		}
		if x.EthTxIndex != 0 {
			i = runtime.EncodeVarint(dAtA, i, uint64(x.EthTxIndex))
			i--
			dAtA[i] = 0x20
		}
		if x.MsgIndex != 0 {
			i = runtime.EncodeVarint(dAtA, i, uint64(x.MsgIndex))
			i--
			dAtA[i] = 0x18
		}
		if x.TxIndex != 0 {
			i = runtime.EncodeVarint(dAtA, i, uint64(x.TxIndex))
			i--
			dAtA[i] = 0x10
		}
		if x.Height != 0 {
			i = runtime.EncodeVarint(dAtA, i, uint64(x.Height))
			i--
			dAtA[i] = 0x8
		}
		if input.Buf != nil {
			input.Buf = append(input.Buf, dAtA...)
		} else {
			input.Buf = dAtA
		}
		return protoiface.MarshalOutput{
			NoUnkeyedLiterals: input.NoUnkeyedLiterals,
			Buf:               input.Buf,
		}, nil
	}
	unmarshal := func(input protoiface.UnmarshalInput) (protoiface.UnmarshalOutput, error) {
		x := input.Message.Interface().(*TxResult)
		if x == nil {
			return protoiface.UnmarshalOutput{
				NoUnkeyedLiterals: input.NoUnkeyedLiterals,
				Flags:             input.Flags,
			}, nil
		}
		options := runtime.UnmarshalInputToOptions(input)
		_ = options
		dAtA := input.Buf
		l := len(dAtA)
		iNdEx := 0
		for iNdEx < l {
			preIndex := iNdEx
			var wire uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
				}
				if iNdEx >= l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				wire |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			fieldNum := int32(wire >> 3)
			wireType := int(wire & 0x7)
			if wireType == 4 {
				return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: TxResult: wiretype end group for non-group")
			}
			if fieldNum <= 0 {
				return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: TxResult: illegal tag %d (wire type %d)", fieldNum, wire)
			}
			switch fieldNum {
			case 1:
				if wireType != 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Height", wireType)
				}
				x.Height = 0
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					x.Height |= int64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
			case 2:
				if wireType != 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field TxIndex", wireType)
				}
				x.TxIndex = 0
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					x.TxIndex |= uint32(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
			case 3:
				if wireType != 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field MsgIndex", wireType)
				}
				x.MsgIndex = 0
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					x.MsgIndex |= uint32(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
			case 4:
				if wireType != 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field EthTxIndex", wireType)
				}
				x.EthTxIndex = 0
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					x.EthTxIndex |= int32(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
			case 5:
				if wireType != 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field Failed", wireType)
				}
				var v int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				x.Failed = bool(v != 0)
			case 6:
				if wireType != 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field GasUsed", wireType)
				}
				x.GasUsed = 0
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					x.GasUsed |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
			case 7:
				if wireType != 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, fmt.Errorf("proto: wrong wireType = %d for field CumulativeGasUsed", wireType)
				}
				x.CumulativeGasUsed = 0
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrIntOverflow
					}
					if iNdEx >= l {
						return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					x.CumulativeGasUsed |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
			default:
				iNdEx = preIndex
				skippy, err := runtime.Skip(dAtA[iNdEx:])
				if err != nil {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, err
				}
				if (skippy < 0) || (iNdEx+skippy) < 0 {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, runtime.ErrInvalidLength
				}
				if (iNdEx + skippy) > l {
					return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
				}
				if !options.DiscardUnknown {
					x.unknownFields = append(x.unknownFields, dAtA[iNdEx:iNdEx+skippy]...)
				}
				iNdEx += skippy
			}
		}

		if iNdEx > l {
			return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, io.ErrUnexpectedEOF
		}
		return protoiface.UnmarshalOutput{NoUnkeyedLiterals: input.NoUnkeyedLiterals, Flags: input.Flags}, nil
	}
	return &protoiface.Methods{
		NoUnkeyedLiterals: struct{}{},
		Flags:             protoiface.SupportMarshalDeterministic | protoiface.SupportUnmarshalDiscardUnknown,
		Size:              size,
		Marshal:           marshal,
		Unmarshal:         unmarshal,
		Merge:             nil,
		CheckInitialized:  nil,
	}
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.27.0
// 	protoc        (unknown)
// source: cosmos/evm/types/v1/indexer.proto

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// TxResult is the value stored in eth tx indexer
type TxResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// height of the blockchain
	Height int64 `protobuf:"varint,1,opt,name=height,proto3" json:"height,omitempty"`
	// tx_index of the cosmos transaction
	TxIndex uint32 `protobuf:"varint,2,opt,name=tx_index,json=txIndex,proto3" json:"tx_index,omitempty"`
	// msg_index in a batch transaction
	MsgIndex uint32 `protobuf:"varint,3,opt,name=msg_index,json=msgIndex,proto3" json:"msg_index,omitempty"`
	// eth_tx_index is the index in the list of valid eth tx in the block,
	// aka. the transaction list returned by eth_getBlock api.
	EthTxIndex int32 `protobuf:"varint,4,opt,name=eth_tx_index,json=ethTxIndex,proto3" json:"eth_tx_index,omitempty"`
	// failed is true if the eth transaction did not go succeed
	Failed bool `protobuf:"varint,5,opt,name=failed,proto3" json:"failed,omitempty"`
	// gas_used by the transaction. If it exceeds the block gas limit,
	// it's set to gas limit, which is what's actually deducted by ante handler.
	GasUsed uint64 `protobuf:"varint,6,opt,name=gas_used,json=gasUsed,proto3" json:"gas_used,omitempty"`
	// cumulative_gas_used specifies the cumulated amount of gas used for all
	// processed messages within the current batch transaction.
	CumulativeGasUsed uint64 `protobuf:"varint,7,opt,name=cumulative_gas_used,json=cumulativeGasUsed,proto3" json:"cumulative_gas_used,omitempty"`
}

func (x *TxResult) Reset() {
	*x = TxResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cosmos_evm_types_v1_indexer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TxResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TxResult) ProtoMessage() {}

// Deprecated: Use TxResult.ProtoReflect.Descriptor instead.
func (*TxResult) Descriptor() ([]byte, []int) {
	return file_cosmos_evm_types_v1_indexer_proto_rawDescGZIP(), []int{0}
}

func (x *TxResult) GetHeight() int64 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *TxResult) GetTxIndex() uint32 {
	if x != nil {
		return x.TxIndex
	}
	return 0
}

func (x *TxResult) GetMsgIndex() uint32 {
	if x != nil {
		return x.MsgIndex
	}
	return 0
}

func (x *TxResult) GetEthTxIndex() int32 {
	if x != nil {
		return x.EthTxIndex
	}
	return 0
}

func (x *TxResult) GetFailed() bool {
	if x != nil {
		return x.Failed
	}
	return false
}

func (x *TxResult) GetGasUsed() uint64 {
	if x != nil {
		return x.GasUsed
	}
	return 0
}

func (x *TxResult) GetCumulativeGasUsed() uint64 {
	if x != nil {
		return x.CumulativeGasUsed
	}
	return 0
}

var File_cosmos_evm_types_v1_indexer_proto protoreflect.FileDescriptor

var file_cosmos_evm_types_v1_indexer_proto_rawDesc = []byte{
	0x0a, 0x21, 0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x2f, 0x65, 0x76, 0x6d, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x65, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x13, 0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x2e, 0x65, 0x76, 0x6d, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x14, 0x67, 0x6f, 0x67, 0x6f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x67, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe5,
	0x01, 0x0a, 0x08, 0x54, 0x78, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x68, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x78, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x74, 0x78, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1b,
	0x0a, 0x09, 0x6d, 0x73, 0x67, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x08, 0x6d, 0x73, 0x67, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x20, 0x0a, 0x0c, 0x65,
	0x74, 0x68, 0x5f, 0x74, 0x78, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x65, 0x74, 0x68, 0x54, 0x78, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x16, 0x0a,
	0x06, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x66,
	0x61, 0x69, 0x6c, 0x65, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x61, 0x73, 0x5f, 0x75, 0x73, 0x65,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x67, 0x61, 0x73, 0x55, 0x73, 0x65, 0x64,
	0x12, 0x2e, 0x0a, 0x13, 0x63, 0x75, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x67,
	0x61, 0x73, 0x5f, 0x75, 0x73, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11, 0x63,
	0x75, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x47, 0x61, 0x73, 0x55, 0x73, 0x65, 0x64,
	0x3a, 0x04, 0x88, 0xa0, 0x1f, 0x00, 0x42, 0xc4, 0x01, 0x0a, 0x17, 0x63, 0x6f, 0x6d, 0x2e, 0x63,
	0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x2e, 0x65, 0x76, 0x6d, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x2e,
	0x76, 0x31, 0x42, 0x0c, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x50, 0x01, 0x5a, 0x2c, 0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x73, 0x64, 0x6b, 0x2e, 0x69, 0x6f,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x2f, 0x65, 0x76, 0x6d, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x31,
	0xa2, 0x02, 0x03, 0x43, 0x45, 0x54, 0xaa, 0x02, 0x13, 0x43, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x2e,
	0x45, 0x76, 0x6d, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2e, 0x56, 0x31, 0xca, 0x02, 0x13, 0x43,
	0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x5c, 0x45, 0x76, 0x6d, 0x5c, 0x54, 0x79, 0x70, 0x65, 0x73, 0x5c,
	0x56, 0x31, 0xe2, 0x02, 0x1f, 0x43, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x5c, 0x45, 0x76, 0x6d, 0x5c,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x5c, 0x56, 0x31, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0xea, 0x02, 0x16, 0x43, 0x6f, 0x73, 0x6d, 0x6f, 0x73, 0x3a, 0x3a, 0x45,
	0x76, 0x6d, 0x3a, 0x3a, 0x54, 0x79, 0x70, 0x65, 0x73, 0x3a, 0x3a, 0x56, 0x31, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_cosmos_evm_types_v1_indexer_proto_rawDescOnce sync.Once
	file_cosmos_evm_types_v1_indexer_proto_rawDescData = file_cosmos_evm_types_v1_indexer_proto_rawDesc
)

func file_cosmos_evm_types_v1_indexer_proto_rawDescGZIP() []byte {
	file_cosmos_evm_types_v1_indexer_proto_rawDescOnce.Do(func() {
		file_cosmos_evm_types_v1_indexer_proto_rawDescData = protoimpl.X.CompressGZIP(file_cosmos_evm_types_v1_indexer_proto_rawDescData)
	})
	return file_cosmos_evm_types_v1_indexer_proto_rawDescData
}

var file_cosmos_evm_types_v1_indexer_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_cosmos_evm_types_v1_indexer_proto_goTypes = []interface{}{
	(*TxResult)(nil), // 0: cosmos.evm.types.v1.TxResult
}
var file_cosmos_evm_types_v1_indexer_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_cosmos_evm_types_v1_indexer_proto_init() }
func file_cosmos_evm_types_v1_indexer_proto_init() {
	if File_cosmos_evm_types_v1_indexer_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_cosmos_evm_types_v1_indexer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TxResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_cosmos_evm_types_v1_indexer_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_cosmos_evm_types_v1_indexer_proto_goTypes,
		DependencyIndexes: file_cosmos_evm_types_v1_indexer_proto_depIdxs,
		MessageInfos:      file_cosmos_evm_types_v1_indexer_proto_msgTypes,
	}.Build()
	File_cosmos_evm_types_v1_indexer_proto = out.File
	file_cosmos_evm_types_v1_indexer_proto_rawDesc = nil
	file_cosmos_evm_types_v1_indexer_proto_goTypes = nil
	file_cosmos_evm_types_v1_indexer_proto_depIdxs = nil
}
